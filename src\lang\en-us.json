{"add_to": "Add to ...", "agree": "Sure", "agree_go": "Turn it on", "agree_to": "Go set it", "back": "Back", "back_home": "Back to Desktop", "cancel": "Cancel", "cancel_button_text_2": "No, no, wrong click", "change_position": "Adjust Position", "change_position_list_title": "Adjust the position of the list", "change_position_music_multi_title": "Adjust the position of the selected {num} song to", "change_position_music_title": "Adjust the position of \"{name}\" to", "change_position_tip": "Please enter a new position", "close": "Close", "collect": "Collect", "collect_songlist": "Collect", "collect_success": "Successfully collected", "collect_toplist": "Collect Top playlist", "comment_hide_text": "Fold", "comment_not support": "Unable to get comments for this song.", "comment_refresh": "This is already the comment for \"{name}\"", "comment_show_image": "Show Picture", "comment_show_text": "Expand", "comment_tab_hot": "Top {total}", "comment_tab_new": "Latest {total}", "comment_title": "Comments for \"{name}\"", "confirm": "Confirm", "confirm_button_text": "Yes", "confirm_tip": "Just to double check, do you really want to do this?", "copy_name": "Share Song", "copy_name_tip": "<PERSON>pied", "create_new_folder": "Create new folder", "create_new_folder_error_tip": "The name entered is invalid", "create_new_folder_tip": "Please enter a new folder name", "date_format_hour": "{num} hours ago", "date_format_minute": "{num} minutes ago", "date_format_second": "{num} seconds ago", "deep_link__handle_error_tip": "Call failed: {message}", "deep_link_file_js_confirm_tip": "Are you sure you want to import the \"{name}\" Music API?", "deep_link_file_lxmc_confirm_tip": "Are you sure you want to import the \"{name}\" list file?", "delete": "Remove", "dialog_cancel": "No", "dialog_confirm": "OK", "disagree": "<PERSON><PERSON>", "disagree_tip": "Cancelled...", "dislike": "Dislike", "duplicate_list_tip": "You have collected this list \"{name}\", do you need to update the songs in it?", "edit_metadata": "<PERSON>", "exit_app_tip": "Are you sure you want to exit the app?", "ignoring_battery_optimization_check_tip": "IKUN Music is \"Restricted\" or \"Optimized\" in \"App battery usage\", which may cause IKUN Music to be prevented by the system when playing music in the background. Do you need to set IKUN Music to \"Unrestricted\"?", "ignoring_battery_optimization_check_title": "Background Running Permission Reminder", "input_error": "Don't type indiscriminately 😡", "list_add_btn_title": "Add the song(s) to \"{name}\"", "list_add_tip_exists": "This song already exists in the list, don't click me again~😡", "list_add_title_first_add": "Add", "list_add_title_first_move": "Move", "list_add_title_last": "to...", "list_create": "Create List", "list_create_input_placeholder": "What name do you want...", "list_duplicate_tip": "A list with the same name already exists. Do you want to continue creating it?", "list_edit_action_tip_add_failed": "Failed to add", "list_edit_action_tip_add_success": "Successfully added", "list_edit_action_tip_exist": "This song already exists in this list", "list_edit_action_tip_move_failed": "Failed to move", "list_edit_action_tip_move_success": "Successfully moved", "list_edit_action_tip_remove_success": "Successfully removed", "list_end": "In the end~", "list_error": "Loading failed😥, click to try to reload", "list_export": "Export", "list_export_part_desc": "Choose where to save the list file", "list_import": "Import", "list_import_part_button_cancel": "No", "list_import_part_button_confirm": "Overwrite", "list_import_part_confirm": "The imported list ({importName}) has the same ID as the local list ({localName}). Do you want to overwrite the local list?", "list_import_part_desc": "Choose list file", "list_import_tip__alldata": "This is an \"All Data\" backup file. You need to go here to import:\n\n\"Settings -> Backup & Restore -> List Data -> Import lists\"", "list_import_tip__failed": "Failed to import", "list_import_tip__playlist": "This is a \"List\" backup file. You need to go here to import:\n\n\"Settings -> Backup & Restore -> List Data -> Import lists\"", "list_import_tip__playlist_part": "This is a \"List-only\" backup file. You need to go here to import:\n\n\"Your Library -> Click the button to the right of any list name -> Click \"Import\" in the menu\"", "list_import_tip__setting": "This is a \"Settings\" backup file. the mobile terminal does not support importing such files", "list_import_tip__unknown": "Unknown file type. Please try to upgrade the app to the latest version and try again.", "list_loading": "Loading...", "list_multi_add_title_first_add": "Add the selected", "list_multi_add_title_first_move": "Move the selected", "list_multi_add_title_last": "songs to ...", "list_name_default": "<PERSON><PERSON><PERSON>", "list_name_love": "Loved", "list_name_temp": "Temp List", "list_remove": "Remove", "list_remove_music_multi_tip": "Do you really want to remove the selected {num} songs?", "list_remove_tip": "Do you really want to remove \"{name}\"?", "list_remove_tip_button": "Yes, that's right", "list_rename": "<PERSON><PERSON>", "list_rename_title": "Rename List", "list_select_all": "Select All", "list_select_cancel": "Cancel", "list_select_local_file": "Add Local Songs", "list_select_local_file_desc": "Choose local song folder", "list_select_local_file_empty_tip": "No songs found in current folder", "list_select_local_file_result_failed_tip": "Found {total} song(s), successfully added {success} song(s), failed to add {failed} song(s). View the error log for details.", "list_select_local_file_result_tip": "Found {Total} song(s), all added!", "list_select_local_file_temp_add_tip": "Found {total} matching files, quickly added to the current list, will now start the file metadata reading process. Please do not exit the app!", "list_select_range": "Range", "list_select_single": "Single", "list_select_unall": "Select none", "list_sort": "Sort Songs", "list_sort_modal_by_album": "Album", "list_sort_modal_by_down": "Descending", "list_sort_modal_by_field": "Sort Field", "list_sort_modal_by_name": "Title", "list_sort_modal_by_random": "Random", "list_sort_modal_by_singer": "Artist", "list_sort_modal_by_source": "Music Service", "list_sort_modal_by_time": "Length", "list_sort_modal_by_type": "Sort Category", "list_sort_modal_by_up": "Ascending", "list_sync": "Update", "list_sync_confirm_tip": "This will replace the songs in \"{name}\" with the songs in the online list, are you sure you want to update?", "list_update_error": "Failed to update \"{name}\"", "list_update_success": "Successfully updated \"{name}\"", "list_updating": "Updating", "lists__duplicate": "Duplicate Songs", "lists_dislike_music_add_tip": "Added", "lists_dislike_music_singer_tip": "Do you really dislike {singer}'s \"{name}\"?", "lists_dislike_music_tip": "Do you really dislike \"{name}\"?", "load_failed": "Ah, the loading failed 😥", "loading": "Loading...", "location": "From {location}", "lyric__load_error": "Failed to get lyrics", "metadata_edit_modal_confirm": "Save", "metadata_edit_modal_failed": "Failed to save. Please view the error log for details.", "metadata_edit_modal_file_name": "File Name", "metadata_edit_modal_file_path": "File Path", "metadata_edit_modal_form_album_name": "Album", "metadata_edit_modal_form_lyric": "LRC Lyric", "metadata_edit_modal_form_match_lyric": "Matching online", "metadata_edit_modal_form_match_lyric_failed": "Failed to match lyrics online", "metadata_edit_modal_form_match_lyric_success": "Successfully matched lyric🎉", "metadata_edit_modal_form_match_pic": "Matching online", "metadata_edit_modal_form_match_pic_failed": "Failed to match cover online", "metadata_edit_modal_form_match_pic_success": "Successfully matched cover🎉", "metadata_edit_modal_form_name": "Title", "metadata_edit_modal_form_parse_name": "Parse song title and artist from file name", "metadata_edit_modal_form_parse_name_singer": "Title - Artist", "metadata_edit_modal_form_parse_singer_name": "Artist - Title", "metadata_edit_modal_form_pic": "Album Cover", "metadata_edit_modal_form_remove_lyric": "Clear", "metadata_edit_modal_form_remove_pic": "Remove image", "metadata_edit_modal_form_select_pic": "Choose image", "metadata_edit_modal_form_select_pic_title": "Choose album cover image", "metadata_edit_modal_form_singer": "Artist", "metadata_edit_modal_processing": "Writing...", "metadata_edit_modal_success": "Successfully saved", "metadata_edit_modal_tip": "Song title cannot be empty", "metadata_edit_modal_title": "Edit Song <PERSON>", "move_to": "Move to...", "music_source_detail": "Song Detail", "name": "Title: {name}", "nav_exit": "Exit App", "nav_love": "Your Library", "nav_search": "Search", "nav_setting": "Settings", "nav_songlist": "Playlists", "nav_top": "Charts", "never_show": "Never show again", "no_item": "The list is empty...", "notifications_check_tip": "You have not allowed IKUN Music to show notifications, or the \"MusicService\" notification category in the IKUN Music notification settings is disabled, which prevents you from using the notification for actions such as pausing, switching songs, and so on. Do you want to enable it?", "notifications_check_title": "Notification Permission Reminder", "ok": "OK", "open_storage_error_tip": "The entered path is illegal", "open_storage_not_found_title": "External memory card not found. Manually enter the path to specify external memory in the following", "open_storage_select_managed_folder_failed_tip": "Failed to select storage path: {msg}", "open_storage_select_path": "Open storage path", "open_storage_select_path_tip": "TIP: For external storage, if you still cannot access it after granting storage permissions, you can click the following button to select the path to allow access.", "open_storage_tip": "Enter storage path", "open_storage_title": "Manually enter the path to specify external memory in the following", "parent_dir_name": "Parent folder", "pause": "Pause", "play": "Play", "play_all": "Play all", "play_detail_setting_lrc_align": "Lyric Alignment", "play_detail_setting_lrc_align_center": "Center", "play_detail_setting_lrc_align_left": "Left", "play_detail_setting_lrc_align_right": "Right", "play_detail_setting_lrc_font_size": "Lyric Font Size", "play_detail_setting_playback_rate": "Playback Rate", "play_detail_setting_playback_rate_reset": "Reset", "play_detail_setting_show_lyric_progress_setting": "Allow to adjust playback progress by drag-and-drop lyrics", "play_detail_setting_title": "Player Options", "play_detail_setting_volume": "Volume", "play_detail_todo_tip": "What do you want? No, this function has not been implemented yet 😛, But you can try to locate the currently playing song by long pressing (Only valid for playing songs in \"Your Library\")", "play_later": "Play Later", "play_list_loop": "Repeat Playlist", "play_list_order": "In order", "play_list_random": "Shuffle", "play_next": "Next Song", "play_prev": "Prev Song", "play_single": "Disable", "play_single_loop": "Repeat", "player__buffering": "Buffering...", "player__end": "Finished", "player__error": "Error loading music. Switch to the next song after 5 seconds", "player__getting_url": "Getting music link...", "player__getting_url_delay_retry": "The server is busy. Try again in {time} seconds...", "player__loading": "Music loading...", "player__refresh_url": "Music URL expired, refreshing...", "player_cache_migrating": "Song cache is being migrated, please wait ⌛️", "quality_high_quality": "HQ", "quality_lossless": "SQ", "quality_lossless_24bit": "24bit", "quality_lossless_atmos": "Atmos", "quality_lossless_atmos_plus": "Atmos2.0", "quality_lossless_master": "Master", "search__welcome": "Search what I want~~😉", "search_history_search": "Search History", "search_hot_search": "Top Searches", "search_type_music": "Song", "search_type_songlist": "Playlist", "setting__other_dislike_list": "Dislike <PERSON>", "setting__other_dislike_list_label": "Number of rules: {num}", "setting__other_dislike_list_saved_tip": "Saved", "setting__other_lyric_raw_clear_btn": "<PERSON> L<PERSON>", "setting__other_lyric_raw_label": "Number of lyrics:", "setting__other_meta_cache": "Other Cache Management", "setting__other_music_url_clear_btn": "Clear Song URL Cache", "setting__other_music_url_label": "Number of song URLs: ", "setting__other_other_source_clear_btn": "Clear Song Cache of Changed Source", "setting__other_other_source_label": "Number of songs information that changed source: ", "setting__other_resource_cache": "Resource Cache Management", "setting_about": "About IKUN Music", "setting_backup": "Backup & Restore", "setting_backup_all": "All data (\"List\" data and \"Setting\" data)", "setting_backup_all_export": "Export", "setting_backup_all_export_desc": "Save the backup to...", "setting_backup_all_import": "Import", "setting_backup_all_import_desc": "Choose a backup file", "setting_backup_part": "List Data (Compatible with the desktop edition of list backup files)", "setting_backup_part_export_list": "Export lists", "setting_backup_part_export_list_desc": "Save the lists to ...", "setting_backup_part_export_list_tip_failed": "Failed to export playlists", "setting_backup_part_export_list_tip_success": "Successfully exported", "setting_backup_part_export_list_tip_zip": "📦The file is being packaged...\nIf the file is too large, it may take some time⏳", "setting_backup_part_export_setting": "Export settings", "setting_backup_part_export_setting_desc": "Save the settings to ...", "setting_backup_part_import_list": "Import lists", "setting_backup_part_import_list_desc": "Choose a list backup file", "setting_backup_part_import_list_tip_error": "Failed to import list 😕", "setting_backup_part_import_list_tip_running": "🚀I am trying to import...\nIf the list is too big, it may take some time⏳", "setting_backup_part_import_list_tip_success": "Successfully imported 🎉", "setting_backup_part_import_list_tip_unzip": "📦File parsing...\nIf the file is too large, it may take some time⏳", "setting_backup_part_import_setting": "Import settings", "setting_backup_part_import_setting_desc": "Choose a setting backup file", "setting_basic": "General", "setting_basic_always_keep_statusbar_height": "Always keep status bar height", "setting_basic_always_keep_statusbar_height_tip": "By default, the app dynamically determines whether spacing needs to be kept for the system status bar, but if there is a situation on your device where the app's interactable content overlaps with the display of the status bar content, you can enable this option to always keep space for the system status bar.", "setting_basic_animation": "Randomize pop-up animation", "setting_basic_auto_hide_play_bar": "Hide playbar when keyboard pops up", "setting_basic_drawer_layout_position": "Direction of Navigation & List Popup", "setting_basic_drawer_layout_position_left": "Left Side", "setting_basic_drawer_layout_position_right": "Right Side", "setting_basic_font_size": "<PERSON><PERSON> (Effective After Restart)", "setting_basic_font_size_100": "Standard", "setting_basic_font_size_110": "Big", "setting_basic_font_size_120": "Larger", "setting_basic_font_size_130": "Oversize", "setting_basic_font_size_80": "Smaller", "setting_basic_font_size_90": "Small", "setting_basic_font_size_preview": "IKUN Music Font Size Preview", "setting_basic_home_page_scroll": "Allow horizontal scrolling on vertical homepage", "setting_basic_lang": "Language", "setting_basic_share_type": "Share", "setting_basic_share_type_clipboard": "Copy to clipboard", "setting_basic_share_type_system": "Use system share", "setting_basic_show_animation": "Show animation", "setting_basic_show_back_btn": "Show \"Back to Desktop\" button", "setting_basic_show_exit_btn": "Show \"Exit App\" button", "setting_basic_source": "Music API", "setting_basic_source_direct": "Trial API", "setting_basic_source_status_failed": "Failed to initialize", "setting_basic_source_status_initing": "Initializing...", "setting_basic_source_status_success": "Successfully initialized", "setting_basic_source_temp": "Temporary API (Some features not available. Workaround if Test API is unavailable)", "setting_basic_source_test": "Test API (Available for most app features)", "setting_basic_source_title": "Choose a Music API", "setting_basic_source_user_api_btn": "Music API Management", "setting_basic_sourcename": "Music Streaming Service Name", "setting_basic_sourcename_alias": "<PERSON><PERSON>", "setting_basic_sourcename_real": "Original", "setting_basic_sourcename_title": "Choose the music streaming service name", "setting_basic_startup_auto_play": "Automatically play music on startup", "setting_basic_startup_push_play_detail_screen": "Open the playback detail page on startup", "setting_basic_theme": "Theme", "setting_basic_theme_auto_theme": "Follow system light & dark modes to switch themes", "setting_basic_theme_dynamic_bg": "Use dynamic backgrounds", "setting_basic_theme_font_shadow": "Enable font shadow", "setting_basic_theme_hide_bg_dark": "Hide the background of the black theme", "setting_basic_theme_more_btn_show": "More themes", "setting_basic_use_system_file_selector": "Use the system file selector", "setting_basic_use_system_file_selector_tip": "When this option is enabled, operations such as importing backup files, Music APIs, etc. will not need to request storage permissions, but may not be available on some systems.\n\nYou can turn off the option to fallback to the app's built-in file selector if you are unable to import files after enabling the option.", "setting_dislike_list_input_tip": "song_name@artist_name\nsong_name\n@artist_name", "setting_dislike_list_tips": "1. One line per entry. If there is an \"@\" symbol in the name of the song or artist, it needs to be replaced with \"#\"\n2. Specify a song by a certain artist: song_name@artist_name\n3. Specify a song: song_name\n4. Specify an artist: @artist_name", "setting_list": "List", "setting_list_add_music_location_type": "Position When Adding a Song to the List", "setting_list_add_music_location_type_bottom": "Bottom", "setting_list_add_music_location_type_top": "Top", "setting_list_click_action": "Automatically switch to current list when clicking a song in the list (Only valid for \"Playlists\" and \"Charts\" page)", "setting_list_show interval": "Show song length", "setting_list_show_album_name": "Show song album name", "setting_lyric_desktop": "Desktop Lyric", "setting_lyric_desktop_enable": "Show lyric window", "setting_lyric_desktop_lock": "Lock lyric window", "setting_lyric_desktop_maxlineNum": "Maximum Number of Lines", "setting_lyric_desktop_permission_tip": "To use this feature, you need to grant IKUN Music the permission to display hover windows in the system permission settings.\n\nDo you go to the relevant page to grant this permission?", "setting_lyric_desktop_single_line": "Do not wrap lyrics", "setting_lyric_desktop_text_opacity": "Lyric Font Transparency", "setting_lyric_desktop_text_size": "Lyric Font Size", "setting_lyric_desktop_text_x": "Lyric Horizontal Alignment", "setting_lyric_desktop_text_x_center": "Center", "setting_lyric_desktop_text_x_left": "Left", "setting_lyric_desktop_text_x_right": "Right", "setting_lyric_desktop_text_y": "Lyric Vertical Alignment", "setting_lyric_desktop_text_y_bottom": "Bottom", "setting_lyric_desktop_text_y_center": "Center", "setting_lyric_desktop_text_y_top": "Top", "setting_lyric_desktop_theme": "Lyric Theme Color", "setting_lyric_desktop_toggle_anima": "Show lyric switching animation", "setting_lyric_desktop_view_width": "Percentage of Window Width", "setting_other": "Extras", "setting_other_cache": "Cache Management (including the cache of songs, lyrics, error logs, etc., it is not recommended to clean up if there is no problem related to song playback)", "setting_other_cache_clear_btn": "<PERSON>ache", "setting_other_cache_clear_success_tip": "Cache cleanup complete", "setting_other_cache_getting": "Cache being counted...", "setting_other_cache_size": "The app has used cache size: ", "setting_other_dislike_list_show_btn": "Edit Rules", "setting_other_log": "Error log (When abnormal operation occurs)", "setting_other_log_btn_clean": "Clear", "setting_other_log_btn_hide": "Close", "setting_other_log_btn_show": "View log", "setting_other_log_sync_log": "Logging sync log", "setting_other_log_tip_clean_success": "Log cleanup complete", "setting_other_log_tip_null": "The log is empty~", "setting_other_log_user_api_log": "Logging Music API log", "setting_play_audio_offload": "Enable audio offload", "setting_play_audio_offload_tip": "You can enable this option to save power consumption, but on some devices there may be an issue where all songs prompt \"Error loading music\" or \"Unable to play the whole song in full\", this is due to a bug in the current system. \n\nFor those experiencing this issue you can turn off the option and restart the app completely and try again.", "setting_play_auto_clean_played_list": "Automatically empty the played list", "setting_play_auto_clean_played_list_tip": "In the shuffle mode, when switching between songs by clicking on a song in the same list as playlist, if \"Automatically empty played list\" is enabled, the songs in the list will participate in the random again.", "setting_play_cache_size": "Maximum <PERSON><PERSON> (MB)", "setting_play_cache_size_no_cache": "<PERSON><PERSON> Disabled", "setting_play_cache_size_save_tip": "The cache is set and takes effect after restarting the app.", "setting_play_handle_audio_focus": "Automatically pause playback when other apps playing sound", "setting_play_handle_audio_focus_tip": "Take effect after restarting the app", "setting_play_lyric_transition": "Show translated lyrics", "setting_play_play_quality": "Prioritize Sound Quality for Playback If Available", "setting_play_s2t": "Convert Chinese lyrics that are playing to traditional", "setting_play_save_play_time": "Remember playback progress", "setting_play_show_bluetooth_lyric": "Show lyrics from bluetooth", "setting_play_show_notification_image": "Show album cover in notification bar", "setting_play_show_roma": "Show romanized lyrics if available", "setting_play_show_translation": "Show translated lyrics if available", "setting_player": "Playback", "setting_player_save_play_time": "Remember playback progress", "setting_search": "Search", "setting_search_show_history_search": "Enable Search History", "setting_search_show_hot_search": "Enable Top Searches", "setting_sync": "Sync", "setting_sync_address": "Current device address: {address}", "setting_sync_code_blocked_ip": "The IP of the current device has been blocked by the server!", "setting_sync_code_fail": "Invalid connection code", "setting_sync_code_input_tip": "Please enter the connection code", "setting_sync_code_label": "Need to enter the connection code for the first connection", "setting_sync_enable": "Enable sync", "setting_sync_history": "History address", "setting_sync_history_empty": "Nothing here", "setting_sync_history_title": "Connection history", "setting_sync_host_label": "Synch service address", "setting_sync_host_value_error_tip": "The address needs to start with \"http://\" or \"https://\"!", "setting_sync_host_value_tip": "http://<Host>:<Port>", "setting_sync_port_label": "Sync service port number", "setting_sync_port_tip": "Please enter a port number", "setting_sync_status": "Status: {status}", "setting_sync_status_enabled": "connected", "setting_theme": "Theme", "setting_version": "Update", "setting_version_show_ver_modal": "Open Update Window 🚀", "share_card_title_music": "Share \"{name}\" to...", "share_title_music": "Song Sharing", "singer": "Artist: {name}", "songlist_hot": "Top", "songlist_hot_collect": "Top Collect", "songlist_new": "Latest", "songlist_open": "Open", "songlist_open_input_placeholder": "Enter a playlist link/ID", "songlist_open_input_tip": "1. Cross-service playlists are not supported. Please confirm whether the playlist to be opened corresponds to the current chosen service\n2. If you encounter a playlist link that cannot be opened. Please send us your feedback\n3. Unable to open Kugou playlist with playlist ID or link from lite edition, but support to open it with Kugou code or link from main edition", "songlist_recommend": "Recommend", "songlist_rise": "Rise", "songlist_tag_default": "<PERSON><PERSON><PERSON>", "songlist_tag_hot": "Top", "songlist_tags": "Playlist Category", "source_alias_all": "Aggregated", "source_alias_bd": "BD Music", "source_alias_kg": "KG Music", "source_alias_kw": "KW Music", "source_alias_mg": "MG Music", "source_alias_tx": "TX Music", "source_alias_wy": "WY Music", "source_real_all": "Aggregated", "source_real_bd": "Baidu", "source_real_kg": "Ku<PERSON><PERSON>", "source_real_kw": "<PERSON><PERSON>", "source_real_mg": "<PERSON><PERSON>", "source_real_tx": "Tencent", "source_real_wy": "NetEase", "stop": "Stop", "stopped": "Stopped", "storage_file_no_match": "The selected file does not meet the requirements!", "storage_file_no_select_file_failed_tip": "Failed to select a file using the system file selector. Do you want to fallback to the app's built-in file selector?", "storage_permission_tip_disagree": "The user rejects it!", "storage_permission_tip_disagree_ask_again": "The feature is unavailable because you have permanently denied IKUN Music access to the internal storage.\n\nTo continue, you need to navigate to IKUN Music's \"Permissions\" option in the system settings and allow IKUN Music to access the files.", "storage_permission_tip_request": "To use this feature you need to allow IKUN Music to access the internal storage, do you agree and continue?", "sync__dislike_mode_merge_tip_desc": "Merge the content of the two lists and remove the duplicates.", "sync__dislike_mode_other_tip_desc": "\"Cancel Sync\" will not sync the \"dislike song\" list.", "sync__dislike_mode_overwrite_tip_desc": "The list of overwritten parties will be replaced with the list of overwriting parties.", "sync__dislike_mode_title": "Choose how to sync with {name}'s \"dislike song\" list", "sync__list_mode_merge_tip_desc": "Merge the two lists together. The same song will be removed (the song of the merged person is removed), and different songs will be added.", "sync__list_mode_other_tip_desc": "\"Cancel Sync\" will not sync the list.", "sync__list_mode_overwrite_tip_desc": "Lists with the same ID as the overwritten list and the overwritten list will be deleted and replaced with the overrider's list (lists with different list IDs will be merged together). If \"Full Overwrite\" is checked, all lists of the covered one will be moved. Remove and replace with a list of overrides.", "sync__list_mode_title": "Choose how to synchronize the list with \"{name}\"", "sync__mode_merge_btn_local_remote": "\"Local List\" Merge \"Remote List\"", "sync__mode_merge_btn_remote_local": "\"Remote List\" Merge \"Local List\"", "sync__mode_merge_tip": "Merge: ", "sync__mode_other_label": "Other", "sync__mode_other_tip": "Other: ", "sync__mode_overwrite": "Full Overwrite", "sync__mode_overwrite_btn_cancel": "Cancel Sync", "sync__mode_overwrite_btn_local_remote": "\"Local List\" Overwrite \"Remote List\"", "sync__mode_overwrite_btn_remote_local": "\"Remote List\" Overwrite \"Local List\"", "sync__mode_overwrite_label": "Overwrite", "sync__mode_overwrite_tip": "Overwrite: ", "sync_status_disabled": "not connected", "theme_black": "Black", "theme_blue": "Blue", "theme_blue2": "Purple Blue", "theme_blue_plus": "Blue Plus", "theme_brown": "<PERSON>", "theme_china_ink": "China Ink", "theme_green": "Green", "theme_grey": "Grey", "theme_happy_new_year": "New Year", "theme_mid_autumn": "Mid-Autumn", "theme_ming": "<PERSON>", "theme_naruto": "<PERSON><PERSON><PERSON>", "theme_orange": "Orange", "theme_pink": "Pink", "theme_purple": "Purple", "theme_red": "Red", "timeout_exit_btn_cancel": "Cancel timing", "timeout_exit_btn_update": "Update timing", "timeout_exit_btn_wait_cancel": "Cancel exit", "timeout_exit_btn_wait_tip": "Timeout expired, waiting to exit...", "timeout_exit_input_tip": "Enter countdown minutes", "timeout_exit_label_isPlayed": "Wait for the song to finish playing and then stop playing", "timeout_exit_min": "Minutes", "timeout_exit_tip_cancel": "Timeout stop playing has been cancelled", "timeout_exit_tip_max": "You can only set up to {num} minutes", "timeout_exit_tip_off": "Set timer to stop playing", "timeout_exit_tip_on": "Stop playing after {time}", "toggle_source": "Change Source", "toggle_source_failed": "Failed to change the source. Please try to manually search for the song on the search page by specifying another service.", "toggle_source_try": "Try switching to another source...", "understand": "Already understood 👌", "user_api__init_failed_alert": "Failed to initialize Music API \"{name}\":", "user_api_add_failed_tip": "Invalid Music API file", "user_api_allow_show_update_alert": "Allow show update popups", "user_api_btn_import": "Import", "user_api_btn_import_local": "From Local", "user_api_btn_import_online": "From Network", "user_api_btn_import_online_input_confirm": "Import", "user_api_btn_import_online_input_loading": "Importing...", "user_api_btn_import_online_input_tip": "Please enter an HTTP link", "user_api_empty": "It's actually empty here 😲", "user_api_import_desc": "Choose Music API script file", "user_api_import_failed_tip": "Failed to import Music API: \n{message}", "user_api_import_success_tip": "Successfully imported 🎉", "user_api_max_tip": "There can only be a maximum of 20 APIs at the same time🤪.\n\nIf you want to continue importing, please remove some unnecessary APIs to make room.", "user_api_note": "TIP: Although we have isolated the API script's running environment as much as possible, importing API scripts containing malicious behaviors may still affect your system. Please import them with caution.", "user_api_readme": "API writing instructions: ", "user_api_remove_tip": "Do you really want to remove \"{name}\"?", "user_api_title": "Music API Management (EXPERIMENTAL)", "user_api_update_alert": "Music API \"{name}\" found new version", "user_api_update_alert_open_url": "Open update address", "download_music_title": "Download {name} - {artist}", "version_btn_close": "Close", "version_btn_downloading": "I am trying to download... {current}/{total} ({progress}%)", "version_btn_failed": "Retry", "version_btn_ignore": "Ignore", "version_btn_ignore_cancel": "Cancel ignore", "version_btn_min": "Background download", "version_btn_new": "Update", "version_btn_unknown": "Project Homepage", "version_btn_update": "Install", "version_label_change_log": "Changelog: ", "version_label_current_ver": "Current: ", "version_label_history": "History: ", "version_label_latest_ver": "Latest: ", "version_tip_checking": "Checking for updates...⏳", "version_tip_downloaded": "The apk has been downloaded.", "version_tip_failed": "Failed to download the apk. You can either retry or go to the project address and download the new update manually.", "version_tip_latest": "The app is up to date, please enjoy it~🥂", "version_tip_min": "The download has been switched to background. You can navigate to \"Settings → Update\" to re-open this pop-up window.", "version_tip_unknown": "Failed to get the latest version information. Recommended to manually go to the project address to check for a new version.", "version_title_checking": "⏳ Checking for updates ⏳", "version_title_failed": "❌ Failed to download ❌", "version_title_latest": "🎉 The current version is already the latest 🎊", "version_title_new": "🌟 New version found 🌟", "version_title_unknown": "❓ Failed to get the latest version information ❓", "version_title_update": "🚀 App update 🚀", "128k": "128K", "320k": "320K", "flac": "FLAC", "hires": "24-Bit <PERSON><PERSON>", "atmos": "Atmos", "atmos_plus": "Atmos 2.0", "master": "Master"}