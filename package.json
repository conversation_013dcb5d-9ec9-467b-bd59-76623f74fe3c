{"name": "ikun-music-mobile", "version": "1.7.6", "versionCode": 70, "private": true, "scripts": {"dev": "react-native run-android --active-arch-only", "start": "react-native start", "sc": "react-native start --reset-cache", "rd": "react-devtools", "menu": "adb shell input keyevent 82", "bundle-android": "react-native bundle --platform android --dev true --entry-file index.js --bundle-output android/app/src/main/assets/index.android.bundle --assets-dest android/app/src/main/res", "build-test": "react-native bundle --platform android --dev true --entry-file index.js --bundle-output index.android.bundle --assets-dest res", "pack:android:debug": "./gradlew assembleDebug", "pack": "npm run pack:android", "pack:android": "cd android && gradlew.bat assembleRelease", "clear": "cd android && gradlew.bat clean", "clear:full": "git clean -fdx -e android/keystore.properties -e android/app/*.keystore", "build:theme": "node src/theme/themes/createThemes.js", "publish": "node publish"}, "engines": {"node": ">= 18", "npm": ">= 8.5.2"}, "repository": {"type": "git", "url": "git+https://github.com/ikunshare/ikun-music-mobile.git"}, "keywords": ["music-player", "react-native-app"], "author": {"name": "lyswhut", "email": "<EMAIL>"}, "license": "Apache-2.0", "bugs": {"url": "https://github.com/ikunshare/ikun-music-mobile/issues"}, "homepage": "https://github.com/ikunshare/ikun-music-mobile#readme", "dependencies": {"@craftzdog/react-native-buffer": "^6.0.5", "@react-native-async-storage/async-storage": "^2.1.1", "@react-native-clipboard/clipboard": "^1.16.1", "@react-native-community/slider": "^4.5.5", "he": "^1.2.0", "iconv-lite": "^0.6.3", "lrc-file-parser": "^2.4.1", "message2call": "^0.1.3", "pako": "^2.1.0", "react": "18.2.0", "react-native": "0.73.11", "react-native-background-timer": "github:lyswhut/react-native-background-timer#55ecaa80880e9cec1fff81f3ce10e6250ab3c40c", "react-native-exception-handler": "^2.10.10", "react-native-file-system": "github:lyswhut/react-native-file-system#2a37b90dbb8d37c5180777d0bf0d4f160812b0c1", "react-native-fs": "^2.20.0", "react-native-local-media-metadata": "github:lyswhut/react-native-local-media-metadata#f2d03999413fa8fc9b0a25cde07c18d1a56988f0", "react-native-navigation": "7.39.2", "react-native-pager-view": "6.7.0", "react-native-quick-base64": "^2.1.2", "react-native-quick-md5": "^3.0.6", "react-native-track-player": "github:lyswhut/react-native-track-player#75c097a4a46bea19970540bbc1bd38527ea73cde", "react-native-vector-icons": "^10.2.0", "rn-fetch-blob": "^0.12.0"}, "devDependencies": {"@babel/core": "^7.26.7", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/preset-env": "^7.26.7", "@babel/runtime": "^7.26.7", "@react-native/babel-preset": "^0.74.89", "@react-native/metro-config": "^0.74.89", "@react-native/typescript-config": "^0.74.89", "@tsconfig/react-native": "^3.0.5", "@types/he": "^1.2.3", "@types/react": "^18.3.18", "@types/react-native": "^0.72.8", "@types/react-native-background-timer": "^2.0.2", "@types/react-native-vector-icons": "^6.4.18", "babel-plugin-module-resolver": "^5.0.2", "changelog-parser": "^3.0.1", "typescript": "^5.7.3"}}