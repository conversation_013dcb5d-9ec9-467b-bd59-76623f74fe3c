import { memo, useCallback, useState } from 'react'
import { View, TouchableOpacity, ScrollView } from 'react-native'

import { useTheme } from '@/store/theme/hook'
import { createStyle } from '@/utils/tools'
import Text from '@/components/common/Text'
import { SETTING_SCREENS, type SettingScreenIds } from '../Main'
import { useI18n } from '@/lang'
import { BorderRadius, BorderWidths } from '@/theme'

const ListItem = memo(
  ({
    id,
    activeId,
    onPress,
  }: {
    onPress: (item: SettingScreenIds) => void
    activeId: string
    id: SettingScreenIds
  }) => {
    const theme = useTheme()
    const t = useI18n()

    const active = activeId == id

    const handlePress = () => {
      onPress(id)
    }

    return (
      <View
        style={{
          ...styles.listItem,
          backgroundColor: active ? theme['c-primary-background-active'] : 'transparent',
        }}
      >
        <TouchableOpacity style={styles.listName} onPress={handlePress}>
          <Text numberOfLines={1} color={active ? theme['c-primary-font'] : theme['c-font']}>
            {t(`setting_${id}`)}
          </Text>
        </TouchableOpacity>
      </View>
    )
  },
  (prevProps, nextProps) => {
    return !!(
      prevProps.id === nextProps.id &&
      prevProps.activeId != nextProps.id &&
      nextProps.activeId != nextProps.id
    )
  }
)

export default ({ onChangeId }: { onChangeId: (id: SettingScreenIds) => void }) => {
  const [activeId, setActiveId] = useState(global.lx.settingActiveId)
  const theme = useTheme()

  const handleChangeId = useCallback((id: SettingScreenIds) => {
    onChangeId(id)
    setActiveId(id)
    global.lx.settingActiveId = id
  }, [])

  return (
    <ScrollView
      horizontal
      style={{ ...styles.container, borderBottomColor: theme['c-border-background'] }}
      contentContainerStyle={styles.contentContainer}
      keyboardShouldPersistTaps={'always'}
    >
      {SETTING_SCREENS.map((id) => (
        <ListItem key={id} id={id} activeId={activeId} onPress={handleChangeId} />
      ))}
    </ScrollView>
  )
}

const styles = createStyle({
  container: {
    height: 50,
    flexGrow: 0,
    flexShrink: 0,
    borderBottomWidth: BorderWidths.normal,
    opacity: 0.7,
  },
  contentContainer: {
    flexDirection: 'row',
    flexWrap: 'nowrap',
    padding: 5,
    // backgroundColor: 'rgba(0, 0, 0, 0.1)',
  },
  // listContainer: {
  //   // borderBottomWidth: BorderWidths.normal2,
  // },

  listItem: {
    // width: '33.33%',
    height: 40,
    paddingLeft: 15,
    paddingRight: 15,
    // height: 'auto',
    // flexDirection: 'row',
    // alignItems: 'center',
    paddingHorizontal: 5,
    // paddingVertical: 10,
    borderRadius: BorderRadius.normal,
    marginBottom: 5,
    // backgroundColor: 'rgba(0,0,0,0.1)',
  },
  listName: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    // paddingLeft: 5,
    // backgroundColor: 'rgba(0,0,0,0.1)',
  },
})
