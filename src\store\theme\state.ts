import { createContext } from 'react'
// import type { RootState } from '@/store'

interface InitState {
  shouldUseDarkColors: boolean
  theme: LX.ActiveTheme
}

const theme = {
  id: '',
  name: '',
  isDark: false,
  'c-primary': 'rgb(77, 175, 124)',
  'c-primary-dark-100': 'rgb(69,158,112)',
  'c-primary-dark-100-alpha-100': 'rgba(69, 158, 112, 0.90)',
  'c-primary-alpha-100': 'rgba(77, 175, 124, 0.90)',
  'c-primary-dark-100-alpha-200': 'rgba(69, 158, 112, 0.80)',
  'c-primary-alpha-200': 'rgba(77, 175, 124, 0.80)',
  'c-primary-dark-100-alpha-300': 'rgba(69, 158, 112, 0.70)',
  'c-primary-alpha-300': 'rgba(77, 175, 124, 0.70)',
  'c-primary-dark-100-alpha-400': 'rgba(69, 158, 112, 0.60)',
  'c-primary-alpha-400': 'rgba(77, 175, 124, 0.60)',
  'c-primary-dark-100-alpha-500': 'rgba(69, 158, 112, 0.50)',
  'c-primary-alpha-500': 'rgba(77, 175, 124, 0.50)',
  'c-primary-dark-100-alpha-600': 'rgba(69, 158, 112, 0.40)',
  'c-primary-alpha-600': 'rgba(77, 175, 124, 0.40)',
  'c-primary-dark-100-alpha-700': 'rgba(69, 158, 112, 0.30)',
  'c-primary-alpha-700': 'rgba(77, 175, 124, 0.30)',
  'c-primary-dark-100-alpha-800': 'rgba(69, 158, 112, 0.20)',
  'c-primary-alpha-800': 'rgba(77, 175, 124, 0.20)',
  'c-primary-dark-100-alpha-900': 'rgba(69, 158, 112, 0.10)',
  'c-primary-alpha-900': 'rgba(77, 175, 124, 0.10)',
  'c-primary-dark-200': 'rgb(62,142,101)',
  'c-primary-dark-200-alpha-100': 'rgba(62, 142, 101, 0.90)',
  'c-primary-dark-200-alpha-200': 'rgba(62, 142, 101, 0.80)',
  'c-primary-dark-200-alpha-300': 'rgba(62, 142, 101, 0.70)',
  'c-primary-dark-200-alpha-400': 'rgba(62, 142, 101, 0.60)',
  'c-primary-dark-200-alpha-500': 'rgba(62, 142, 101, 0.50)',
  'c-primary-dark-200-alpha-600': 'rgba(62, 142, 101, 0.40)',
  'c-primary-dark-200-alpha-700': 'rgba(62, 142, 101, 0.30)',
  'c-primary-dark-200-alpha-800': 'rgba(62, 142, 101, 0.20)',
  'c-primary-dark-200-alpha-900': 'rgba(62, 142, 101, 0.10)',
  'c-primary-dark-300': 'rgb(56,128,91)',
  'c-primary-dark-300-alpha-100': 'rgba(56, 128, 91, 0.90)',
  'c-primary-dark-300-alpha-200': 'rgba(56, 128, 91, 0.80)',
  'c-primary-dark-300-alpha-300': 'rgba(56, 128, 91, 0.70)',
  'c-primary-dark-300-alpha-400': 'rgba(56, 128, 91, 0.60)',
  'c-primary-dark-300-alpha-500': 'rgba(56, 128, 91, 0.50)',
  'c-primary-dark-300-alpha-600': 'rgba(56, 128, 91, 0.40)',
  'c-primary-dark-300-alpha-700': 'rgba(56, 128, 91, 0.30)',
  'c-primary-dark-300-alpha-800': 'rgba(56, 128, 91, 0.20)',
  'c-primary-dark-300-alpha-900': 'rgba(56, 128, 91, 0.10)',
  'c-primary-dark-400': 'rgb(50,115,82)',
  'c-primary-dark-400-alpha-100': 'rgba(50, 115, 82, 0.90)',
  'c-primary-dark-400-alpha-200': 'rgba(50, 115, 82, 0.80)',
  'c-primary-dark-400-alpha-300': 'rgba(50, 115, 82, 0.70)',
  'c-primary-dark-400-alpha-400': 'rgba(50, 115, 82, 0.60)',
  'c-primary-dark-400-alpha-500': 'rgba(50, 115, 82, 0.50)',
  'c-primary-dark-400-alpha-600': 'rgba(50, 115, 82, 0.40)',
  'c-primary-dark-400-alpha-700': 'rgba(50, 115, 82, 0.30)',
  'c-primary-dark-400-alpha-800': 'rgba(50, 115, 82, 0.20)',
  'c-primary-dark-400-alpha-900': 'rgba(50, 115, 82, 0.10)',
  'c-primary-dark-500': 'rgb(45,104,74)',
  'c-primary-dark-500-alpha-100': 'rgba(45, 104, 74, 0.90)',
  'c-primary-dark-500-alpha-200': 'rgba(45, 104, 74, 0.80)',
  'c-primary-dark-500-alpha-300': 'rgba(45, 104, 74, 0.70)',
  'c-primary-dark-500-alpha-400': 'rgba(45, 104, 74, 0.60)',
  'c-primary-dark-500-alpha-500': 'rgba(45, 104, 74, 0.50)',
  'c-primary-dark-500-alpha-600': 'rgba(45, 104, 74, 0.40)',
  'c-primary-dark-500-alpha-700': 'rgba(45, 104, 74, 0.30)',
  'c-primary-dark-500-alpha-800': 'rgba(45, 104, 74, 0.20)',
  'c-primary-dark-500-alpha-900': 'rgba(45, 104, 74, 0.10)',
  'c-primary-dark-600': 'rgb(41,94,67)',
  'c-primary-dark-600-alpha-100': 'rgba(41, 94, 67, 0.90)',
  'c-primary-dark-600-alpha-200': 'rgba(41, 94, 67, 0.80)',
  'c-primary-dark-600-alpha-300': 'rgba(41, 94, 67, 0.70)',
  'c-primary-dark-600-alpha-400': 'rgba(41, 94, 67, 0.60)',
  'c-primary-dark-600-alpha-500': 'rgba(41, 94, 67, 0.50)',
  'c-primary-dark-600-alpha-600': 'rgba(41, 94, 67, 0.40)',
  'c-primary-dark-600-alpha-700': 'rgba(41, 94, 67, 0.30)',
  'c-primary-dark-600-alpha-800': 'rgba(41, 94, 67, 0.20)',
  'c-primary-dark-600-alpha-900': 'rgba(41, 94, 67, 0.10)',
  'c-primary-dark-700': 'rgb(37,85,60)',
  'c-primary-dark-700-alpha-100': 'rgba(37, 85, 60, 0.90)',
  'c-primary-dark-700-alpha-200': 'rgba(37, 85, 60, 0.80)',
  'c-primary-dark-700-alpha-300': 'rgba(37, 85, 60, 0.70)',
  'c-primary-dark-700-alpha-400': 'rgba(37, 85, 60, 0.60)',
  'c-primary-dark-700-alpha-500': 'rgba(37, 85, 60, 0.50)',
  'c-primary-dark-700-alpha-600': 'rgba(37, 85, 60, 0.40)',
  'c-primary-dark-700-alpha-700': 'rgba(37, 85, 60, 0.30)',
  'c-primary-dark-700-alpha-800': 'rgba(37, 85, 60, 0.20)',
  'c-primary-dark-700-alpha-900': 'rgba(37, 85, 60, 0.10)',
  'c-primary-dark-800': 'rgb(33,77,54)',
  'c-primary-dark-800-alpha-100': 'rgba(33, 77, 54, 0.90)',
  'c-primary-dark-800-alpha-200': 'rgba(33, 77, 54, 0.80)',
  'c-primary-dark-800-alpha-300': 'rgba(33, 77, 54, 0.70)',
  'c-primary-dark-800-alpha-400': 'rgba(33, 77, 54, 0.60)',
  'c-primary-dark-800-alpha-500': 'rgba(33, 77, 54, 0.50)',
  'c-primary-dark-800-alpha-600': 'rgba(33, 77, 54, 0.40)',
  'c-primary-dark-800-alpha-700': 'rgba(33, 77, 54, 0.30)',
  'c-primary-dark-800-alpha-800': 'rgba(33, 77, 54, 0.20)',
  'c-primary-dark-800-alpha-900': 'rgba(33, 77, 54, 0.10)',
  'c-primary-dark-900': 'rgb(30,69,49)',
  'c-primary-dark-900-alpha-100': 'rgba(30, 69, 49, 0.90)',
  'c-primary-dark-900-alpha-200': 'rgba(30, 69, 49, 0.80)',
  'c-primary-dark-900-alpha-300': 'rgba(30, 69, 49, 0.70)',
  'c-primary-dark-900-alpha-400': 'rgba(30, 69, 49, 0.60)',
  'c-primary-dark-900-alpha-500': 'rgba(30, 69, 49, 0.50)',
  'c-primary-dark-900-alpha-600': 'rgba(30, 69, 49, 0.40)',
  'c-primary-dark-900-alpha-700': 'rgba(30, 69, 49, 0.30)',
  'c-primary-dark-900-alpha-800': 'rgba(30, 69, 49, 0.20)',
  'c-primary-dark-900-alpha-900': 'rgba(30, 69, 49, 0.10)',
  'c-primary-dark-1000': 'rgb(27,62,44)',
  'c-primary-dark-1000-alpha-100': 'rgba(27, 62, 44, 0.90)',
  'c-primary-dark-1000-alpha-200': 'rgba(27, 62, 44, 0.80)',
  'c-primary-dark-1000-alpha-300': 'rgba(27, 62, 44, 0.70)',
  'c-primary-dark-1000-alpha-400': 'rgba(27, 62, 44, 0.60)',
  'c-primary-dark-1000-alpha-500': 'rgba(27, 62, 44, 0.50)',
  'c-primary-dark-1000-alpha-600': 'rgba(27, 62, 44, 0.40)',
  'c-primary-dark-1000-alpha-700': 'rgba(27, 62, 44, 0.30)',
  'c-primary-dark-1000-alpha-800': 'rgba(27, 62, 44, 0.20)',
  'c-primary-dark-1000-alpha-900': 'rgba(27, 62, 44, 0.10)',
  'c-primary-light-100': 'rgb(113,191,150)',
  'c-primary-light-100-alpha-100': 'rgba(113, 191, 150, 0.90)',
  'c-primary-light-100-alpha-200': 'rgba(113, 191, 150, 0.80)',
  'c-primary-light-100-alpha-300': 'rgba(113, 191, 150, 0.70)',
  'c-primary-light-100-alpha-400': 'rgba(113, 191, 150, 0.60)',
  'c-primary-light-100-alpha-500': 'rgba(113, 191, 150, 0.50)',
  'c-primary-light-100-alpha-600': 'rgba(113, 191, 150, 0.40)',
  'c-primary-light-100-alpha-700': 'rgba(113, 191, 150, 0.30)',
  'c-primary-light-100-alpha-800': 'rgba(113, 191, 150, 0.20)',
  'c-primary-light-100-alpha-900': 'rgba(113, 191, 150, 0.10)',
  'c-primary-light-200': 'rgb(141,204,171)',
  'c-primary-light-200-alpha-100': 'rgba(141, 204, 171, 0.90)',
  'c-primary-light-200-alpha-200': 'rgba(141, 204, 171, 0.80)',
  'c-primary-light-200-alpha-300': 'rgba(141, 204, 171, 0.70)',
  'c-primary-light-200-alpha-400': 'rgba(141, 204, 171, 0.60)',
  'c-primary-light-200-alpha-500': 'rgba(141, 204, 171, 0.50)',
  'c-primary-light-200-alpha-600': 'rgba(141, 204, 171, 0.40)',
  'c-primary-light-200-alpha-700': 'rgba(141, 204, 171, 0.30)',
  'c-primary-light-200-alpha-800': 'rgba(141, 204, 171, 0.20)',
  'c-primary-light-200-alpha-900': 'rgba(141, 204, 171, 0.10)',
  'c-primary-light-300': 'rgb(164,214,188)',
  'c-primary-light-300-alpha-100': 'rgba(164, 214, 188, 0.90)',
  'c-primary-light-300-alpha-200': 'rgba(164, 214, 188, 0.80)',
  'c-primary-light-300-alpha-300': 'rgba(164, 214, 188, 0.70)',
  'c-primary-light-300-alpha-400': 'rgba(164, 214, 188, 0.60)',
  'c-primary-light-300-alpha-500': 'rgba(164, 214, 188, 0.50)',
  'c-primary-light-300-alpha-600': 'rgba(164, 214, 188, 0.40)',
  'c-primary-light-300-alpha-700': 'rgba(164, 214, 188, 0.30)',
  'c-primary-light-300-alpha-800': 'rgba(164, 214, 188, 0.20)',
  'c-primary-light-300-alpha-900': 'rgba(164, 214, 188, 0.10)',
  'c-primary-light-400': 'rgb(182,222,201)',
  'c-primary-light-400-alpha-100': 'rgba(182, 222, 201, 0.90)',
  'c-primary-light-400-alpha-200': 'rgba(182, 222, 201, 0.80)',
  'c-primary-light-400-alpha-300': 'rgba(182, 222, 201, 0.70)',
  'c-primary-light-400-alpha-400': 'rgba(182, 222, 201, 0.60)',
  'c-primary-light-400-alpha-500': 'rgba(182, 222, 201, 0.50)',
  'c-primary-light-400-alpha-600': 'rgba(182, 222, 201, 0.40)',
  'c-primary-light-400-alpha-700': 'rgba(182, 222, 201, 0.30)',
  'c-primary-light-400-alpha-800': 'rgba(182, 222, 201, 0.20)',
  'c-primary-light-400-alpha-900': 'rgba(182, 222, 201, 0.10)',
  'c-primary-light-500': 'rgb(197,229,212)',
  'c-primary-light-500-alpha-100': 'rgba(197, 229, 212, 0.90)',
  'c-primary-light-500-alpha-200': 'rgba(197, 229, 212, 0.80)',
  'c-primary-light-500-alpha-300': 'rgba(197, 229, 212, 0.70)',
  'c-primary-light-500-alpha-400': 'rgba(197, 229, 212, 0.60)',
  'c-primary-light-500-alpha-500': 'rgba(197, 229, 212, 0.50)',
  'c-primary-light-500-alpha-600': 'rgba(197, 229, 212, 0.40)',
  'c-primary-light-500-alpha-700': 'rgba(197, 229, 212, 0.30)',
  'c-primary-light-500-alpha-800': 'rgba(197, 229, 212, 0.20)',
  'c-primary-light-500-alpha-900': 'rgba(197, 229, 212, 0.10)',
  'c-primary-light-600': 'rgb(209,234,221)',
  'c-primary-light-600-alpha-100': 'rgba(209, 234, 221, 0.90)',
  'c-primary-light-600-alpha-200': 'rgba(209, 234, 221, 0.80)',
  'c-primary-light-600-alpha-300': 'rgba(209, 234, 221, 0.70)',
  'c-primary-light-600-alpha-400': 'rgba(209, 234, 221, 0.60)',
  'c-primary-light-600-alpha-500': 'rgba(209, 234, 221, 0.50)',
  'c-primary-light-600-alpha-600': 'rgba(209, 234, 221, 0.40)',
  'c-primary-light-600-alpha-700': 'rgba(209, 234, 221, 0.30)',
  'c-primary-light-600-alpha-800': 'rgba(209, 234, 221, 0.20)',
  'c-primary-light-600-alpha-900': 'rgba(209, 234, 221, 0.10)',
  'c-primary-light-700': 'rgb(218,238,228)',
  'c-primary-light-700-alpha-100': 'rgba(218, 238, 228, 0.90)',
  'c-primary-light-700-alpha-200': 'rgba(218, 238, 228, 0.80)',
  'c-primary-light-700-alpha-300': 'rgba(218, 238, 228, 0.70)',
  'c-primary-light-700-alpha-400': 'rgba(218, 238, 228, 0.60)',
  'c-primary-light-700-alpha-500': 'rgba(218, 238, 228, 0.50)',
  'c-primary-light-700-alpha-600': 'rgba(218, 238, 228, 0.40)',
  'c-primary-light-700-alpha-700': 'rgba(218, 238, 228, 0.30)',
  'c-primary-light-700-alpha-800': 'rgba(218, 238, 228, 0.20)',
  'c-primary-light-700-alpha-900': 'rgba(218, 238, 228, 0.10)',
  'c-primary-light-800': 'rgb(225,241,233)',
  'c-primary-light-800-alpha-100': 'rgba(225, 241, 233, 0.90)',
  'c-primary-light-800-alpha-200': 'rgba(225, 241, 233, 0.80)',
  'c-primary-light-800-alpha-300': 'rgba(225, 241, 233, 0.70)',
  'c-primary-light-800-alpha-400': 'rgba(225, 241, 233, 0.60)',
  'c-primary-light-800-alpha-500': 'rgba(225, 241, 233, 0.50)',
  'c-primary-light-800-alpha-600': 'rgba(225, 241, 233, 0.40)',
  'c-primary-light-800-alpha-700': 'rgba(225, 241, 233, 0.30)',
  'c-primary-light-800-alpha-800': 'rgba(225, 241, 233, 0.20)',
  'c-primary-light-800-alpha-900': 'rgba(225, 241, 233, 0.10)',
  'c-primary-light-900': 'rgb(231,244,237)',
  'c-primary-light-900-alpha-100': 'rgba(231, 244, 237, 0.90)',
  'c-primary-light-900-alpha-200': 'rgba(231, 244, 237, 0.80)',
  'c-primary-light-900-alpha-300': 'rgba(231, 244, 237, 0.70)',
  'c-primary-light-900-alpha-400': 'rgba(231, 244, 237, 0.60)',
  'c-primary-light-900-alpha-500': 'rgba(231, 244, 237, 0.50)',
  'c-primary-light-900-alpha-600': 'rgba(231, 244, 237, 0.40)',
  'c-primary-light-900-alpha-700': 'rgba(231, 244, 237, 0.30)',
  'c-primary-light-900-alpha-800': 'rgba(231, 244, 237, 0.20)',
  'c-primary-light-900-alpha-900': 'rgba(231, 244, 237, 0.10)',
  'c-primary-light-1000': 'rgb(255,255,255)',
  'c-primary-light-1000-alpha-100': 'rgba(255, 255, 255, 0.90)',
  'c-primary-light-1000-alpha-200': 'rgba(255, 255, 255, 0.80)',
  'c-primary-light-1000-alpha-300': 'rgba(255, 255, 255, 0.70)',
  'c-primary-light-1000-alpha-400': 'rgba(255, 255, 255, 0.60)',
  'c-primary-light-1000-alpha-500': 'rgba(255, 255, 255, 0.50)',
  'c-primary-light-1000-alpha-600': 'rgba(255, 255, 255, 0.40)',
  'c-primary-light-1000-alpha-700': 'rgba(255, 255, 255, 0.30)',
  'c-primary-light-1000-alpha-800': 'rgba(255, 255, 255, 0.20)',
  'c-primary-light-1000-alpha-900': 'rgba(255, 255, 255, 0.10)',
  'c-theme': 'rgb(77, 175, 124)',

  'c-000': 'rgb(255,255,255)',
  'c-050': 'rgb(244,244,244)',
  'c-100': 'rgb(233,233,233)',
  'c-150': 'rgb(222,222,222)',
  'c-200': 'rgb(211,211,211)',
  'c-250': 'rgb(200,200,200)',
  'c-300': 'rgb(188,188,188)',
  'c-350': 'rgb(177,177,177)',
  'c-400': 'rgb(166,166,166)',
  'c-450': 'rgb(155,155,155)',
  'c-500': 'rgb(144,144,144)',
  'c-550': 'rgb(133,133,133)',
  'c-600': 'rgb(122,122,122)',
  'c-650': 'rgb(111,111,111)',
  'c-700': 'rgb(100,100,100)',
  'c-750': 'rgb(89,89,89)',
  'c-800': 'rgb(77,77,77)',
  'c-850': 'rgb(66,66,66)',
  'c-900': 'rgb(55,55,55)',
  'c-950': 'rgb(44,44,44)',
  'c-1000': 'rgb(33, 33, 33)',
}

const state: InitState = {
  shouldUseDarkColors: false,
  theme: {
    ...theme,
    'c-app-background': theme['c-primary-light-600-alpha-600'],
    'c-main-background': 'rgba(255, 255, 255, 0.9)',

    'c-badge-primary': theme['c-primary'],
    'c-badge-secondary': '#4baed5',
    'c-badge-tertiary': '#e7aa36',

    'c-font': theme['c-850'],
    'c-font-label': theme['c-450'],
    'c-primary-font': theme['c-primary'],
    'c-primary-font-hover': theme['c-primary-alpha-300'],
    'c-primary-font-active': theme['c-primary-dark-100-alpha-200'],
    'c-primary-background': theme['c-primary-light-400-alpha-700'],
    'c-primary-background-hover': theme['c-primary-light-300-alpha-800'],
    'c-primary-background-active': theme['c-primary-light-100-alpha-800'],
    'c-primary-input-background': theme['c-primary-light-400-alpha-700'],
    'c-button-font': theme['c-primary-alpha-100'],
    'c-button-font-selected': theme['c-primary-dark-100-alpha-100'],
    'c-button-background': theme['c-primary-light-400-alpha-700'],
    'c-button-background-selected': theme['c-primary-alpha-600'],
    'c-button-background-hover': theme['c-primary-light-300-alpha-600'],
    'c-button-background-active': theme['c-primary-light-100-alpha-600'],
    'c-list-header-border-bottom': theme['c-primary-alpha-900'],
    'c-content-background': theme['c-primary-light-1000'],
    'c-border-background': theme['c-primary-light-100-alpha-700'],

    'bg-image-position': 'center',
    'bg-image-size': 'cover',
  },
}

export const ThemeContext = createContext(state.theme)

export default state
