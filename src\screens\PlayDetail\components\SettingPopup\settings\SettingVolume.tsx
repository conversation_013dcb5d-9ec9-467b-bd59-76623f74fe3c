import { useState } from 'react'

import { View } from 'react-native'
import { useTheme } from '@/store/theme/hook'
import Text from '@/components/common/Text'
import { useSettingValue } from '@/store/setting/hook'
import Slider, { type SliderProps } from '@/components/common/Slider'
import { updateSetting } from '@/core/common'
import { useI18n } from '@/lang'
import styles from './style'
import { setVolume } from '@/plugins/player'

const Volume = () => {
  const theme = useTheme()
  const volume = Math.trunc(useSettingValue('player.volume') * 100)
  const [sliderSize, setSliderSize] = useState(volume)
  const [isSliding, setSliding] = useState(false)
  const t = useI18n()

  const handleSlidingStart: SliderProps['onSlidingStart'] = (value) => {
    setSliding(true)
  }
  const handleValueChange: SliderProps['onValueChange'] = (value) => {
    value = Math.trunc(value)
    setSliderSize(value)
    void setVolume(value / 100)
  }
  const handleSlidingComplete: SliderProps['onSlidingComplete'] = (value) => {
    setSliding(false)
    value = Math.trunc(value)
    if (volume == value) return
    updateSetting({ 'player.volume': value / 100 })
  }

  return (
    <View style={styles.container}>
      <Text>{t('play_detail_setting_volume')}</Text>
      <View style={styles.content}>
        <Text style={styles.label} color={theme['c-font-label']}>
          {isSliding ? sliderSize : volume}
        </Text>
        <Slider
          minimumValue={0}
          maximumValue={100}
          onSlidingComplete={handleSlidingComplete}
          onValueChange={handleValueChange}
          onSlidingStart={handleSlidingStart}
          step={1}
          value={volume}
        />
      </View>
    </View>
  )
}

export default Volume
