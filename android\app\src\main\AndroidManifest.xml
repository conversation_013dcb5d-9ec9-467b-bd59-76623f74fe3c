<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  package="com.ikunshare.music.mobile">

    <!-- 获取读写外置存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:networkSecurityConfig="@xml/network_security_config"
      android:requestLegacyExternalStorage="true"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:theme="@style/AppTheme"
      tools:targetApi="n">
      <activity
        android:name=".MainActivity"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="adjustResize"
        android:exported="true">
        <intent-filter>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter>
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="lxmusic" android:host="*" />
        </intent-filter>
        <intent-filter android:priority="80">
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT"/>
          <category android:name="android.intent.category.OPENABLE"/>

          <data android:scheme="file"/>
          <data android:scheme="content" />
          <data android:mimeType="audio/*" />
          <data android:mimeType="vnd.android.cursor.dir/audio"/>
          <data android:mimeType="application/ogg"/>
          <data android:mimeType="application/x-ogg"/>
        </intent-filter>
        <intent-filter>
          <action android:name="android.intent.action.MAIN" />
          <category android:name="android.intent.category.APP_MUSIC" />
        </intent-filter>
        <intent-filter>
          <action android:name="com.cyanogenmod.eleven.AUDIO_PLAYER"/>
          <category android:name="android.intent.category.DEFAULT"/>
        </intent-filter>

        <intent-filter android:priority="80">
          <!-- <action android:name="android.intent.action.PICK"/> -->
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT"/>
          <category android:name="android.intent.category.OPENABLE"/>

          <data android:scheme="file"/>
          <data android:scheme="content" />
          <data android:host="*"/>
          <data android:mimeType="application/octet-stream"/>

          <data android:mimeType="text/javascript"/>
          <data android:mimeType="application/x-javascript"/>
          <data android:mimeType="application/javascript"/>
          <!-- https://stackoverflow.com/a/66402243 -->
          <data android:pathPattern=".*\\.js" />
          <data android:pathPattern=".*\\..*\\.js" />
          <data android:pathPattern=".*\\..*\\..*\\.js" />
          <data android:pathSuffix=".js" tools:targetApi="s" />

          <data android:mimeType="application/json"/>
          <data android:pathPattern=".*\\.json" />
          <data android:pathPattern=".*\\..*\\.json" />
          <data android:pathPattern=".*\\..*\\..*\\.json" />
          <data android:pathSuffix=".json" tools:targetApi="s" />

          <data android:pathPattern=".*\\.lxmc" />
          <data android:pathPattern=".*\\..*\\.lxmc" />
          <data android:pathPattern=".*\\..*\\..*\\.lxmc" />
          <data android:pathSuffix=".lxmc" tools:targetApi="s" />
        </intent-filter>
      </activity>

      <!-- Define a FileProvider for API24+ -->
      <!-- note this is the authority name used by other modules like rn-fetch-blob, easy to have conflicts -->
      <provider
        android:name="androidx.core.content.FileProvider"
        android:authorities="${applicationId}.provider"
        android:exported="false"
        android:grantUriPermissions="true">
        <!-- you might need the tools:replace thing to workaround rn-fetch-blob or other definitions of provider -->
        <!-- just make sure if you "replace" here that you include all the paths you are replacing *plus* the cache path we use -->
        <meta-data tools:replace="android:resource"
          android:name="android.support.FILE_PROVIDER_PATHS"
          android:resource="@xml/file_paths" />
      </provider>
    </application>
</manifest>
