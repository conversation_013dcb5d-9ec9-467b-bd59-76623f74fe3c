// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 21
        compileSdkVersion = 35
        targetSdkVersion = 29

        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24" // Or any version above 1.3.x
        RNNKotlinVersion = kotlinVersion

        // https://github.com/DylanVann/react-native-fast-image/blob/9ab80fcd570b7f56da66ab20e52c9a35934067c9/docs/app-glide-module.md
        excludeAppGlideModule = true
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath('com.android.tools.build:gradle:8.6.1')
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

apply plugin: "com.facebook.react.rootproject"
