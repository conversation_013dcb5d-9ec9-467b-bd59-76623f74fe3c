import { forwardRef, useImperativeHandle, useMemo, useRef } from 'react'
import { View, TouchableOpacity } from 'react-native'

import Modal, { type ModalType } from './Modal'
import { Icon } from '@/components/common/Icon'
import { useKeyboard } from '@/utils/hooks'
import { createStyle } from '@/utils/tools'
import { useTheme } from '@/store/theme/hook'
import Text from './Text'
import { useStatusbarHeight } from '@/store/common/hook'

const styles = createStyle({
  centeredView: {
    flex: 1,
    // justifyContent: 'flex-end',
    // alignItems: 'center',
  },
  modalView: {
    elevation: 6,
    flexGrow: 0,
    flexShrink: 1,
  },
  header: {
    flex: 0,
    flexDirection: 'row',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  title: {
    paddingLeft: 10,
    paddingRight: 25,
    paddingTop: 10,
    paddingBottom: 10,
    // lineHeight: 20,
  },
  closeBtn: {
    position: 'absolute',
    right: 0,
    // borderTopRightRadius: 8,
    flexGrow: 0,
    flexShrink: 0,
    height: 30,
    width: 30,
    justifyContent: 'center',
    alignItems: 'center',
    // backgroundColor: '#eee',
  },
})

export interface PopupProps {
  onHide?: () => void
  keyHide?: boolean
  bgHide?: boolean
  closeBtn?: boolean
  position?: 'top' | 'left' | 'right' | 'bottom'
  title?: string
  children: React.ReactNode
}

export interface PopupType {
  setVisible: (visible: boolean) => void
}

export default forwardRef<PopupType, PopupProps>(
  (
    {
      onHide = () => {},
      keyHide = true,
      bgHide = true,
      closeBtn = true,
      position = 'bottom',
      title = '',
      children,
    }: PopupProps,
    ref
  ) => {
    const theme = useTheme()
    const { keyboardShown, keyboardHeight } = useKeyboard()
    const statusBarHeight = useStatusbarHeight()

    const modalRef = useRef<ModalType>(null)

    useImperativeHandle(ref, () => ({
      setVisible(visible: boolean) {
        modalRef.current?.setVisible(visible)
      },
    }))

    const closeBtnComponent = useMemo(
      () =>
        closeBtn ? (
          <TouchableOpacity
            style={styles.closeBtn}
            onPress={() => modalRef.current?.setVisible(false)}
          >
            <Icon name="close" style={{ color: theme['c-font-label'] }} size={12} />
          </TouchableOpacity>
        ) : null,
      [closeBtn, theme]
    )

    const [centeredViewStyle, modalViewStyle] = useMemo(() => {
      switch (position) {
        case 'top':
          return [
            {
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              justifyContent: 'flex-start',
            },
            {
              width: '100%',
              maxHeight: '78%',
              minHeight: '20%',
              // backgroundColor: 'white',
            },
          ] as const
        case 'left':
          return [
            {
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              flexDirection: 'row',
              justifyContent: 'flex-start',
            },
            {
              minWidth: '45%',
              maxWidth: '78%',
              height: '100%',
              paddingTop: statusBarHeight,
              // backgroundColor: 'white',
            },
          ] as const
        case 'right':
          return [
            {
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              flexDirection: 'row',
              justifyContent: 'flex-end',
            },
            {
              minWidth: '45%',
              maxWidth: '78%',
              height: '100%',
              paddingTop: statusBarHeight,
              // backgroundColor: 'white',
            },
          ] as const
        case 'bottom':
        default:
          return [
            {
              position: 'absolute',
              left: 0,
              right: 0,
              bottom: 0,
              top: 0,
              justifyContent: 'flex-end',
            },
            {
              width: '100%',
              maxHeight: '78%',
              minHeight: '20%',
              // backgroundColor: 'white',
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8,
            },
          ] as const
      }
    }, [position, statusBarHeight])

    return (
      <Modal
        onHide={onHide}
        keyHide={keyHide}
        bgHide={bgHide}
        bgColor="rgba(50,50,50,.2)"
        ref={modalRef}
      >
        <View
          style={{
            ...styles.centeredView,
            ...centeredViewStyle,
            paddingBottom: keyboardShown ? keyboardHeight : 0,
          }}
        >
          <View
            style={{
              ...styles.modalView,
              ...modalViewStyle,
              backgroundColor: theme['c-content-background'],
            }}
            onStartShouldSetResponder={() => true}
          >
            <View style={styles.header}>
              <Text size={13} style={styles.title} numberOfLines={1}>
                {title}
              </Text>
              {closeBtnComponent}
            </View>
            {children}
          </View>
        </View>
      </Modal>
    )
  }
)
