// import { PureComponent } from 'react'
// import PropTypes from 'prop-types'
// import { Provider } from 'react-redux'
// import { store } from '../store'

// class AppStoreProvider extends PureComponent<{ children: any }> {
//   getChildContext() {
//     return {
//       store,
//     }
//   }

//   static childContextTypes = {
//     store: PropTypes.shape({}),
//   }

//   render() {
//     return (
//       <Provider store={store}>
//         {this.props.children}
//       </Provider>
//     )
//   }
// }

// export default AppStoreProvider
