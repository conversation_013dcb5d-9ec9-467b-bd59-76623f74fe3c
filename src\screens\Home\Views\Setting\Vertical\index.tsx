export { default } from './Main'
// // import { View } from 'react-native'
// import Main from './Main'
// import { createStyle } from '@/utils/tools'

// const Content = () => {
//   return (
//     <View style={styles.container}>
//       <Main />
//     </View>
//   )
// }

// const styles = createStyle({
//   container: {
//     flex: 1,
//     flexDirection: 'column',
//   },
//   // main: {
//   //   paddingLeft: 15,
//   //   paddingRight: 15,
//   //   paddingTop: 15,
//   //   paddingBottom: 15,
//   // },
// })

// export default Content
