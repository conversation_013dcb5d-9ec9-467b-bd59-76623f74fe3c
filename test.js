const {
  publicEncrypt,
  privateDecrypt,
  generateKey<PERSON>air,
  createCipher<PERSON>,
  createDecipheriv,
  constants,
} = require('crypto')

const generateRsaKey = () =>
  new Promise((resolve, reject) => {
    generateKeyPair(
      'rsa',
      {
        modulusLength: 2048, // It holds a number. It is the key size in bits and is applicable for RSA, and DSA algorithm only.
        publicKeyEncoding: {
          type: 'spki', // Note the type is pkcs1 not spki
          format: 'pem',
        },
        privateKeyEncoding: {
          type: 'pkcs8', // Note again the type is set to pkcs1
          format: 'pem',
          // cipher: "aes-256-cbc", //Optional
          // passphrase: "", //Optional
        },
      },
      (err, publicKey, privateKey) => {
        if (err) {
          reject(err)
          return
        }
        resolve({
          publicKey,
          privateKey,
        })
      }
    )
  })

// generateRsaKey().then(({ publicKey, privateKey }) => {
//   console.log(publicKey)
//   console.log(privateKey)
// })

const rsaEncrypt = (buffer, key) => {
  return publicEncrypt({ key, padding: constants.RSA_PKCS1_OAEP_PADDING }, buffer).toString(
    'base64'
  )
}
const rsaDecrypt = (buffer, key) => {
  return privateDecrypt({ key, padding: constants.RSA_PKCS1_OAEP_PADDING }, buffer)
}

const publicKey = `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0rggENU2JbXgYGoQBIyqlBJP76mgMKh8
5gRIUsAwoq/Oj7qEoKYX9jqtnRgAwEPIV7aLMQxGryfm9fGlohDcUPtcF6za5l6L9Szd+0McOCxZ
SY98/pPFdTYnBHRHPrHHYqzqs4y5wPqpFFNrt2z312YS4xy3SYHkooNPxL0OscxejeG9KtmXQmMd
ejm2MxOIuItlqGHpdwInlvY8Wm/gOMvBmPVffsMaNB412xSZA25D3gRNZRO6O28+S2pXRdSbmFX6
DLWQ/xRDJW1QnfbtjbAJ7Xo1X1anS/NEKRpZqHidjjWI43rL/LhcIAt45a1MkxpBEO+1yCivaNCF
E5jyQwIDAQAB
-----END PUBLIC KEY-----
`
const privateKey = `
********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// const encrypted = rsaEncrypt('123', publicKey)
// console.log(encrypted)
// // const encrypted = 'lJVUCFSV2K90ZdCeosUbtek/wZPmqmKR7ShsP2vfheldde6o9e2Qrmj1QojEwsZtjvq61FCmwpX46LkbsLY/jpM17PUZeqQHhqCy4Rz/hIyMCyIQTPwH5907pIwcpQH2XpJ45/hrjkhLhGU9pkZXtr3qkJiRTi0nllu7z6p6Qf0Hx/zYGxe41VVVnq/9t5xkoUyAfknEn1LMAJyJVft4pD43vTn4tz34+cf7GuzlC4xPiUyKC/trDGBW0kEQBPIaRpd7q1ab9x5fg8mffhBSDR3o+PvVuq3UP02MwpoMDs2bnnwzYawuGv87VNsvEHcvTkZDnh8ME9vtbQboLWVD5w=='

// console.log(rsaDecrypt(Buffer.from(encrypted, 'base64'), privateKey).toString())

const aesEncrypt = (buffer, mode, key, iv) => {
  const cipher = createCipheriv(mode, key, iv)
  return Buffer.concat([cipher.update(buffer), cipher.final()])
}

const aesDecrypt = function (cipherBuffer, mode, key, iv) {
  let decipher = createDecipheriv(mode, key, iv)
  return Buffer.concat([decipher.update(cipherBuffer), decipher.final()])
}

// const aesKey = Buffer.from('123456789abcdefg')
// const iv = Buffer.from('012345678901234a')

// // const encryptedAes = aesEncrypt(Buffer.from('hello'), 'aes-128-cbc', aesKey, iv)
// // console.log(encryptedAes)
// // // const encryptedAes = '4zbNfntuHPrHtPvhEVC10Q=='

// // console.log(aesDecrypt(Buffer.from(encryptedAes, 'base64'), 'aes-128-cbc', aesKey, iv).toString())

// // const encryptedAes = aesEncrypt(Buffer.from('hello'), 'aes-128-ecb', aesKey, '')
// // console.log(encryptedAes)
// const encryptedAes = 'oEShKajDOUILq3cVoRv0iw=='

// console.log(aesDecrypt(Buffer.from(encryptedAes, 'base64'), 'aes-128-ecb', aesKey, '').toString())

const text = '{"id":"3779629","n":100000,"p":1}'
const iv = Buffer.from('0102030405060708')
const presetKey = Buffer.from('0CoJUm6Qyw8W8jud')
const secretKey = [56, 70, 110, 77, 99, 98, 51, 117, 67, 98, 85, 73, 118, 80, 104, 70]

// Rn061YcbiMv3hQJlOLNklgQqbcUEF2YyiShXN8kevX3z+iU8j1qHhNEVEoNTNTPQ
// 4dbKbQjGbYdp/Q0bEwCTHcoB8vEQZNc5OUxz6VxScq4AuCYNHwWY44GJrfYuMV7GqQlC/88WdKg4w9ILJGAx5w==

const r1 = aesEncrypt(text, 'aes-128-cbc', presetKey, iv)
console.log(r1.toString('base64'))
console.log(aesEncrypt(r1, 'aes-128-cbc', Buffer.from(secretKey), iv).toString('base64'))
