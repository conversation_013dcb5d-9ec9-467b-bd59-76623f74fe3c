import { memo } from 'react'

import { View } from 'react-native'

import Check<PERSON><PERSON>, { type CheckBoxProps } from '@/components/common/CheckBox'
import { createStyle } from '@/utils/tools'

export default memo((props: CheckBoxProps) => {
  return (
    <View style={styles.container}>
      <CheckBox {...props} />
    </View>
  )
})

const styles = createStyle({
  container: {
    paddingLeft: 25,
    // marginTop: -10,
    // marginBottom: 0,
  },
})
