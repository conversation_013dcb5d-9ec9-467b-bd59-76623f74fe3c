{"version": "1.7.0", "desc": "落雪祝大家新年快乐！\n\n关于之前提到的新项目\n新项目我取名叫 Any Listen，希望它能像它的名字一样让我们能到处任意听歌。\n经过一年多的开发，因各种原因，实际进度比预期的慢，但还是赶在年前发布了第一个web服务预览版，第一个版本仅支持播放服务器上的歌曲，扩展功能暂时未能开放，但已趋于完成，一两个月内可以搞定。\n目前的版本仅是“能用”的状态，因时间关系，部分UI未能重新设计，但后面会继续完善。\n该项目目前的目标用户是拥有自己服务器且上面存储有歌曲的人使用。\n\n项目刚发布，文档未能完善，遇到使用问题或有任何建议欢迎提 issue 交流，\n项目地址： https://github.com/any-listen/any-listen\n\n---\n\n*为了防止歌曲缓存被第三方软件当做垃圾意外清理，歌曲缓存不再存储到缓存目录。若想清理缓存，需去「设置 → 其他 → 资源缓存管理」清理。*\n\n*更新到该版本后首次播放歌曲时，会将之前的歌曲缓存迁移到新位置，需要等待的时间取决于你已缓存资源的大小。*\n\n*感谢 @3gf8jv4dv 对 LX 系列项目翻译、文档等文案的大幅修订优化。*\n\n新增\n- 新增蓝牙歌词支持，可以通过「设置 → 播放设置 → 显示蓝牙歌词」启用（#615）\n- 新增繁体中文语言（#659, @3gf8jv4dv）\n- 将 LX Music 设置为「音乐应用」类，允许将 LX Music 设置为系统默认音乐播放器\n- 支持在程序外使用 LX Music 打开常见音乐文件及 `.js`、`.json`、`.lxmc` 等文件\n\n优化\n- 防止歌曲缓存被第三方软件当做垃圾意外清理\n- 优化正常播放结束时的下一首歌曲播放衔接度，在歌曲即将结束播放时将预获取下一首歌曲的播放链接，减少自动切歌时的等待时间\n- 优化歌曲换源机制，提升换源正确率\n- 首次使用的提示窗口可以通过点击背景或者返回键关闭（#577）\n- 上移 Toast 位置避免遮挡播放模式图标（#603, @sibojia）\n- 优化简体中文文案编排，大幅修订英语文案编排（#658, #660 等, @3gf8jv4dv）\n- 优化应用图标质量，在高版本系统上使用矢量图标\n\n修复\n- 修复导出文件到范围存储类型的目录时，扩展名丢失的问题\n- 修复切换列表播放歌曲时可能会出现播放的歌曲不对应的问题\n- 修复内置列表名称硬编码和语言切换显示的问题（#662）\n- 修复某些情况下进播放详情页时，详情页不显示或应用界面无响应的问题\n- 修复低版本 Android 在某些情况下对 Emoji 字符编码的处理问题\n\n变更\n- 歌曲缓存不再存储到缓存目录\n- 不再长期缓存换源歌曲信息\n\n其他\n- 更新 Readme 文档，优化文案编排（#651, Thanks @3gf8jv4dv）\n- 更新 Issue 模板（#652, @3gf8jv4dv）\n- 更新项目文档（@3gf8jv4dv）\n- 更新 React Native 到 v0.73.11", "history": [{"version": "1.6.0", "desc": "新增\n- 新增 我的列表-歌曲右击菜单-歌曲换源 功能，换源后下次再播放该列表的该歌曲时将优先尝试播放所选源的歌曲，该功能允许你手动指定来源以解决自动换源失败或者换源不准确的问题\n- 新增 Scheme URL 调用支持，调用传参格式与PC端一致，详情看文档说明： https://lyswhut.github.io/lx-music-doc/mobile/scheme-url"}, {"version": "1.5.0", "desc": "我们发布了关于 LX Music 项目发展调整与新项目计划的说明，\n详情看： https://github.com/lyswhut/lx-music-desktop/issues/1912\n\n新增\n- 新增重复歌曲列表，可以方便移除我的列表中的重复歌曲，此列表会列出目标列表里歌曲名相同的歌曲，可在“我的列表”里的列表名菜单中使用（注：该功能与PC端的区别是可以点击歌曲名多选删除）\n- 新增打开当前歌曲详情页菜单，可以在歌曲菜单中使用\n\n修复\n- 修复潜在桌面歌词导致的崩溃问题\n\n其他\n- 更新 React native 到 v0.73.9\n- 更新 exoplayer 到 v1.4.0"}, {"version": "1.4.2", "desc": "我们发布了关于 LX Music 项目发展调整与新项目计划的说明，\n详情看： https://github.com/lyswhut/lx-music-desktop/issues/1912\n\n修复\n- 修复数据存储管理在移除数据时可能出现移除失败的问题"}, {"version": "1.4.1", "desc": "我们发布了关于 LX Music 项目发展调整与新项目计划的说明，\n详情看： https://github.com/lyswhut/lx-music-desktop/issues/1912\n\n修复\n- 修复播放详情页歌词滚动问题（#518）"}, {"version": "1.4.0", "desc": "我们发布了关于 LX Music 项目发展调整与新项目计划的说明，\n详情看： https://github.com/lyswhut/lx-music-desktop/issues/1912\n\n新增\n- 新增 设置-基本设置-启动后打开播放详情界面 设置，默认关闭（#502 @mingcc7）\n\n修复\n- 修复重复的数据初始化调用\n- 修复导入歌单时可能会导致歌单数据存储异常的问题（#500）\n\n变更\n- 设置-播放设置-优先播放320k音质选项改为“优先播放的音质”，允许选择更高优先播放的音质，如果歌曲及音源支持的话（#487）\n\n其他\n- 更新 React native 到 v0.73.8"}, {"version": "1.3.0", "desc": "新增\n- 新增棕色主题“泥牛入海”\n- 新增设置-基本设置-总是保留状态栏高度设置，如果在你的设备上出现软件可交互内容与状态栏内容显示重叠的情况，可以启用该设置以始终为系统状态栏保留空间\n- 新增在线自定义源导入功能，允许通过http/https链接导入自定义源\n\n优化\n- 不再丢弃kg源逐行歌词（@helloplhm-qwq）\n- 支持kw源排行榜显示大小（revert @Folltoshe #1460）\n- 优化本地歌曲换源匹配机制\n\n修复\n- 修复mg歌词在某些情况下获取失败的问题\n- 修复mg歌单搜索（@helloplhm-qwq）\n- 修复kg最新评论无法获取的问题（@helloplhm-qwq）\n\n其他\n- 更新 React native 到 v0.73.6"}, {"version": "1.2.0", "desc": "提前祝大家新年快乐！\n\n新增\n- 新增自定义源（实验性功能），调用方式与PC端一致，但需要注意的是，移动端自定义源的环境与PC端不同，某些环境API不可用，详情看自定义说明文档\n- 新增长按收藏列表名自动跳转列表顶部的功能\n- 新增实验性的添加本地歌曲到我的收藏支持，与PC端类似，在我的收藏的列表菜单中选择歌曲目录，将添加所选目录下的所有歌曲，目前支持mp3/flac/ogg/wav等格式\n- 新增歌曲标签编辑功能，允许编辑本地源且文件存在的歌曲标签信息\n- 新增动态背景，启用后将使用当前播放歌曲封面做APP背景，默认关闭，可到设置-主题设置启用\n- 新增APP全局字体阴影，默认关闭，可到设置-主题设置启用\n- 新增启用竖屏首页横向滚动设置，默认开启（原来的行为），如果你不想要竖屏的首页左右滑动则可以关闭此设置（#397）\n- 新增“使用系统文件选择器”设置，默认启用，启用该选项后，导入备份文件、自定义源等操作将不需要申请存储权限，但可能在某些系统上不可用\n- 播放详情页新增桌面歌词显示/隐藏切换按钮，长按可切换歌词锁定状态\n- 我的列表菜单列表新增“新建列表”菜单\n- 我的列表菜单列表新增“排序歌曲”菜单，可以排序所选列表内的歌曲，排序功能与PC一致\n- 添加 墨·状态栏特别版（版本号包含`sl`）的 release 构建\n\n优化\n- 添加是否忽略电池优化检查，用于提醒用户添加白名单，确保APP后台播放稳定性\n- 在设置界面返回时，不再直接返回桌面，将回到进入设置界面前的界面，在非设置界面返回时才会返回桌面\n- 更新播放栏进度条样式，进度条允许拖动调整进度\n- 优化播放详情页歌曲封面、控制按钮对各尺寸屏幕的适配，修改横屏下的控制栏按钮布局\n- 优化横竖屏界面的展示判断，现在趋于方屏的屏幕按竖屏的方式显示，横屏下的播放栏添加上一曲切歌按钮\n- 添加对wy源某些歌曲有问题的歌词进行修复（#370）\n- 文件选择器允许选择外置存储设备上的路径，添加SD卡、USB存储等外置存储设备的读写支持\n- 图片显示改用第三方的图片组件，支持gif类型的图片显示，尝试解决某些设备上图片过多导致的应用崩溃问题\n- 歌曲评论内容过长时自动折叠，需手动展开\n- 改进本地音乐在线信息的匹配机制\n- 移除播放服务唤醒锁，解决APP在空闲时仍然处于唤醒状态的问题\n- 添加创建同名列表时的二次确认\n\n修复\n- 修复主题背景覆盖不全的问题\n- 修复清理缓存后查看日志时会导致APP崩溃的问题\n- 修复临时列表变更会意外触发同步的问题\n\n变更\n- 在更低版本的安卓上启用跟随系统亮暗主题功能（#317）\n- 由于歌曲评论的图片太大占用较多资源，评论图片不再直接加载，需要点击图片区域后再加载\n- 导入文件（歌单备份、自定义源文件等）默认不再需要设备存储权限，但如果这导致在你的设备上无法选择文件，则可以关闭基本设置的“使用系统文件选择器”设置，回退到原来的文件选择方式\n\n其他\n- 移除所有内置源，由于收到腾讯投诉要求停止提供软件内置的连接到他们平台的在线播放及下载服务，所以从即日（2023年10月18日）起LX本身不再提供上述服务\n- 更新许可协议的排版，使其看起来更加清晰明了，更新数据来源原理说明\n- 更新 React native 到 v0.73.3\n- 核心播放器从 ExoPlayer 迁移到 media3 v1.2.1"}, {"version": "1.1.1", "desc": "落雪提前祝大家中秋快乐~🥮😘！\n\n优化\n- 通过歌曲菜单添加不喜欢歌曲时需要二次确认防止手抖\n- 减慢歌词详情页歌词滚动速度\n- 更改应用窗口大小获取方式，尝试解决在某些设备上的背景、弹出菜单显示问题\n- 优化同步功能错误消息提示，因同步服务版本不匹配导致的连接失败现在将区分提示\n\n修复\n- 修复横屏状态下的歌词滚动位置计算问题\n- 修复切歌时歌词激活行的重置问题\n- 修复更新翻译歌词、罗马音歌词设置后需重启应用才生效的问题，现在更新设置后会立即生效\n\n其他\n- 更新 React native 到 v0.72.5"}, {"version": "1.1.0", "desc": "目前本项目的原始发布地址只有 **GitHub** 及 **蓝奏网盘** （在设置-关于有说明），其他渠道均为第三方转载发布，可信度请自行鉴别。\n\n本项目无微信公众号之类的官方账号，也未在小米、华为、vivo等应用商店发布应用，商店内的“LX Music”、“洛雪音乐”相关的应用全部属于假冒应用，谨防被骗。\n\n本软件完全无广告且无引流（如需要加群、关注公众号之类才能使用或者升级）的行为，若你使用过程中遇到广告或者引流的信息，则表明你当前运行的软件是第三方修改版。\n\n若在升级新版本时提示签名不一致，则表明你手机上的旧版本或者将要安装的新版本中有一方是第三方修改版。\n\n若在升级新版本时提示无法降级安装，则表明你使用的是universal（通用）版安装包（安装包大小20M+），尝试使用arm64-v8a版安装包或者到GitHub下载其他版本安装包。\n\n该版本针对一加、OPPO、Pixel无法播放歌曲（提示音频加载出错，5 秒后切换下一首）或者无法完整播放歌曲的问题做了处理，但如果你使用该版本后问题依然存在，临时的解决方案是去设置-播放设置关闭“音频卸载”选项后完全重启应用\n\n不兼容性变更\n该版本修改了同步协议逻辑，同步功能至少需要PC端v2.4.0或移动端v1.1.0或同步服务v2.0.0版本才能连接使用\n\n新增\n- 新增列表设置-是否显示歌曲专辑名，默认关闭\n- 新增列表设置-是否显示歌曲时长，默认开启\n- 新增是否允许通过歌词调整播放进度功能，默认关闭，可到播放详情页右上角设置开启\n- 新增“不喜欢歌曲”功能，可以在我的列表或者在线列表内歌曲的右击菜单使用，还可以去“设置-其他”手动编辑不喜欢规则，注：“上一曲”、“下一曲”功能将跳过符合“不喜欢歌曲”规则的歌曲，但你仍可以手动播放这些歌曲\n- 新增同步功能对“不喜欢歌曲”列表的同步\n- 新增设置-播放设置-是否启用音频卸载，该设置之前默认是启用的，现在添加开关允许将其关闭，若出现播放器问题可尝试将其关闭\n- 新增设置-播放设置-自动清空已播放列表选项，默认关闭\n\n优化\n- 优化歌单列表歌单封面大小计算方式\n- 调整竖屏下的排行榜布局\n- 调整歌曲列表信息布局\n- 调整横屏下的歌曲列表为两列\n- 调整桌面歌词主题配色，增强歌词字体阴影（#276）\n- 优化数据传输逻辑，列表同步指令使用队列机制，保证列表同步操作的顺序\n- 暂停播放时播放详情歌词页不要自动滚动歌词回播放位置\n- 播放详情页歌词添加延迟滚动及着色动画\n- 优化息屏下的逻辑处理，尽量减少电量消耗\n\n修复\n- 修复wy歌单分类切换无效的问题\n- 修复因插入数字类型的ID导致其意外在末尾追加 .0 导致列表数据异常的问题，同时也可能导致同步数据丢失的问题（此问题会影响PC端，要完全修复这个问题还需要同时将PC端、同步服务更新到最新版本）\n- 修复在线列表、我的列表内的歌曲批量操作后，没有自动取消选择的问题\n- 修复tx热门评论昵称被错误切割的问题 (By: @helloplhm-qwq, @Folltoshe)\n- 修复wy源热搜词失效的问题（@Folltoshe）\n- 修复mg歌单搜索歌单播放数量显示问题\n- 修复搜索提示功能失效的问题（@Folltoshe）\n- 修复潜在导致列表数据不同步的问题\n- 修复kg无评论时的加载处理问题\n- 修复顺序播放时播放完列表的最后一首歌播放按钮状态没有更新的问题（#300）\n\n变更\n- 随机模式下，通过点击与播放列表相同的列表切歌时，将不再清空已播放列表，即已播放的歌曲不再重新参与随机，若想恢复之前的行为可以去设置-播放设置启用清空已播放列表选项\n\n其他\n- 更新 React native 到 v0.72.4"}, {"version": "1.0.6", "desc": "修复\n- 修复wy歌单分类切换无效的问题"}, {"version": "1.0.5", "desc": "优化\n- 增加kg歌单歌曲hires显示（@helloplhm-qwq）\n- 增加tx源热门评论图片显示（@Folltoshe）\n- 支持wy热门评论翻页\n- 微调排行榜列表宽度及字体大小\n\n修复\n- 修复wy我喜欢列表使用token的方式导入，现在移动端可以使用token的方式导入我喜欢列表的音乐了，这意味着从PC端同步过来的歌单也可以在移动端上更新\n- 修复在线列表的多选问题\n- 修复mg搜索不显示时长的问题（@Folltoshe）\n- 修复mg评论加载失败的问题（@Folltoshe）\n- 修复在Android 5.1下报错的问题\n- 修复对存在错误时间标签的歌词的解析\n- 修复聚合搜索时未显示源名称的问题\n- 修复更改音源的列表歌曲颜色的实时更新问题\n\n其他\n- 更新kg、tx、wy等平台排行榜列表\n- 更新react native到v0.71.7"}, {"version": "1.0.4", "desc": "新增\n- 隐藏黑色主题背景设置，默认关闭，可以去设置-主题设置更改\n\n优化\n- 添加歌单分类、排行榜激活指示器\n- 调整设置界面竖屏下的UI布局\n\n修复\n- 修复歌单排序列表滚动重置问题\n- 修复搜索提示列表的显示时机问题\n- 就放tx源歌词获取失败的问题\n- 修复将播放速率调整为0.6后，再次打开设置面板将会导致app崩溃的问题\n- 修复播放详情页设置面板当前音量显示格式问题\n\n其他\n- 升级 React Native 到 v0.71.5"}, {"version": "1.0.3", "desc": "修复\n- 修复歌单详情页内歌曲最多只加载30首的问题"}, {"version": "1.0.2", "desc": "优化\n- 竖屏下的首页允许滑动切换页面（恢复v0.x.x的切页操作）\n- 优化更新语言、主题设置时的流畅度\n\n其他\n- 启用新架构"}, {"version": "1.0.1", "desc": "修复\n- 修复在线列表翻页问题"}, {"version": "1.0.0", "desc": "从v1.0.0起，我们发布了一个独立版的[数据同步服务](https://github.com/lyswhut/lx-music-sync-server#readme)，如果你有服务器，可以将其部署到服务器上作为私人多端同步服务使用，详情看该项目说明\n\n由于该版本涉及旧版数据迁移，建议更新前先到设置-备份与恢复备份歌单\n\n不兼容性变更说明\n- 同步功能，该功能不支持与PC端v2.2.0之前的版本使用\n\n新增\n- 新增聚合搜索，注：由于这个方式需要对各个源的结果进行排序，所以需要以“歌曲名 歌手”的顺序输入（例如：突然的自我 伍佰），否则排序后的结果可能不是你想要的\n- 新增歌单搜索功能\n- 新增热门搜索显示，默认关闭，需要到设置-搜索设置开启\n- 新增搜索历史记录，默认关闭，需要到设置-搜索设置开启\n- 启动软件时自动回到上次的界面，例如上次退出软件时在我的收藏，下次启动软件时会自动进入我的收藏\n- 新增PC端所拥有的内置皮肤\n- 新增界面字体大小设置\n- 新增播放器音量大小设置，可以去播放详情页-播放器设置-音量大小更改\n- 新增播放器播放速率设置，可以去播放详情页-播放器设置-播放速率更改\n- 新增播放详情页歌词对齐方式设置，可以去播放详情页-播放器设置-歌词对齐方式更改\n- 新增是否在左侧导航栏显示返回桌面按钮设置，默认关闭，可以去设置-基本设置-是否显示返回桌面按钮开启\n- 新增是否在左侧导航栏显示退出应用按钮设置，默认关闭，可以去设置-基本设置-是否显示退出应用按钮开启\n- 支持wy源flac hires歌曲类型的显示\n- 添加kg源评论图片展示（@helloplhm-qwq）\n- 支持kg源搜索列表、排行榜flac hires歌曲类型的显示（@helloplhm-qwq, @Folltoshe）\n\n优化（界面/交互/功能）\n- 调整了首页的界面布局\n- 优化大屏幕下的字体大小及界面布局显示\n- 支持wy源flac hires歌曲类型的显示\n- 优化列表数据导入导出的性能，现在进行这些操作应该可以一下子完成且不会再冻结UI了\n- 支持kg源搜索列表flac hires歌曲类型的显示（@helloplhm-qwq）\n\n优化（程序）\n- 优化程序启动性能，优化与程序交互的流畅度\n- 重构整个程序，重新梳理了程序逻辑，使其更容易扩展及维护，将大部分代码从JavaScript迁移到TypeScript\n- 重写配置管理、列表管理功能，使其与PC端同步，更容易复用PC端的代码\n\n修复\n- 修复使用酷狗码无法打开某些类型的歌单的问题\n- 修复tx源某些歌单无法打开的问题\n\n变更\n- 原来播放详情页的歌词字体大小设置改为播放器设置\n\n其他\n- 升级React Native到v0.71.4"}, {"version": "0.15.5", "desc": "修复\n- 修复导入PC端v2列表文件歌曲信息转换丢失的问题\n- 修复上面问题导致的tx源评论加载失败的问题"}, {"version": "0.15.4", "desc": "修复\n- 修复播放详情页歌词翻译、罗马音歌词匹配问题"}, {"version": "0.15.3", "desc": "修复\n- 修复鸿蒙系统下的崩溃问题"}, {"version": "0.15.2", "desc": "修复\n- 修复潜在的歌词解析导致应用崩溃问题"}, {"version": "0.15.1", "desc": "修复\n- 修复某些歌曲的桌面歌词翻译或罗马音没有显示的问题\n- 修复kg某些歌单链接无法打开的问题"}, {"version": "0.15.0", "desc": "新增\n- 支持导入PC端v2版本的列表数据\n- 添加kg源罗马音歌词的支持\n- 支持打开波点音乐歌单（需在酷我源打开）\n\n修复\n- 支持单行多时间标签歌词解析，修复某些歌词会出现时间标签的问题\n- 修复某些类型的kg歌单无法导入的问题\n- 修复异常歌单、歌曲数据导致的崩溃问题（#157）\n\n其他\n- 升级react-native到 v0.68.5"}, {"version": "0.14.3", "desc": "修复\n- 修复因音源的域名到期导致的音源失效的问题"}, {"version": "0.14.2", "desc": "优化\n- 为tx、kw源添加 Flac 24bit 音质显示，注：由于之前没有记录此音质，所以之前收藏的歌曲信息中不包含它\n\n修复\n- 修复排行榜在旋转屏幕后，选中的榜单被重置回第一个的问题\n- 修复企鹅音乐搜索失效的问题"}, {"version": "0.14.1", "desc": "优化\n- 添加“弹出键盘时自动隐藏播放栏”设置，默认启用（原来的行为），若在某些设备上播放栏无法显示时则可以关闭此设置\n- 优化切歌时桌面歌词的切换动画显示\n- 暂停播放时自动隐藏桌面歌词\n- 在我的列表-列表名左侧添加了一个图标，以表示此处可以点击切换列表\n\n修复\n- 修复tx源搜索失效的问题"}, {"version": "0.14.0", "desc": "新增\n- 新增设置-桌面歌词-单行歌词设置，默认关闭，启用后只显示一行歌词，超出窗口宽度自动滚动到末尾\n- 新增设置-桌面歌词-显示歌词切换动画，默认启用，如果你觉得切换动画影响视觉可以将其关闭\n- 新增设置-基本设置-启动后自动播放音乐，默认关闭\n\n优化\n- 支持mg源的歌词翻译（之前添加的歌曲需要去设置清空缓存才会刷新歌词）\n- 添加歌曲列表更新操作的二次确认\n- 添加导入文件错误时的指引提示\n\n修复\n- 修复桌面歌词转繁体设置不立即生效的问题\n- 修复搜索、歌单、排行榜列表可能在切换新内容后出现上次列表内容的残留问题（#118）\n- 修复在某些系统上播放音乐会导致应用崩溃的问题（#129）\n- 修复停止播放后的播放器状态清理问题\n\n文档\n移动版文档已迁移到：<https://lyswhut.github.io/lx-music-doc/mobile>"}, {"version": "0.13.0", "desc": "从这个版本起，你可以将桌面歌词拖动到状态栏上，然后将歌词字体调小后配合新增的歌词窗口宽度、行数设置，模拟出类似状态栏歌词的效果。\n\n如果你的设备装有Xposed框架，可以使用状态栏版（详情看GitHub置顶issue），它通过调用第三方Xposed模块【墨•状态栏歌词】的API支持来状态栏歌词（感谢@ftevxk）。\n但考虑到要依赖第三方应用，并且是Xposed模块，预计用的人会比较少，所以暂不考虑将此特性包含在正式版中。\n\n新增\n- 新增设置-播放设置-显示歌词罗马音，默认关闭，注：目前只有网易源能获取到罗马音歌词（得益于 Binaryify/NeteaseCloudMusicApi/pull/1523），如果你知道其他源的歌词罗马音获取方式，欢迎PR或开issue交流！\n- 新增黑、白桌面歌词主题\n- 桌面歌词新增窗口宽度百分比、最大歌词行数调整设置，允许将歌词拖动到刘海屏状态栏上。提示：有了这组功能你就可以模拟状态栏歌词了\n- 新增设置-播放设置-将播放的歌词转繁体功能（#114）\n\n优化\n- 允许桌面歌词拖动到状态栏上（感谢@ftevxk）\n- 允许选择更新日志弹窗里的文本内容\n- 桌面歌词的最大字体大小允许调整到500（#107）\n\n修复\n- 修复潜在的桌面歌词导致应用崩溃问题\n\n文档\n- 将歌曲添加“稍后播放”后，它们会被放在一个优先级最高的特殊队列中，点击“下一曲”时会消耗该队列中的歌曲，并且无法通过“上一曲”功能播放该队列的上一首歌曲\n- 在切歌时若不是通过“上一曲”、“下一曲”功能切歌（例如直接点击“排行榜列表”、“我的列表”中的歌曲切歌），“稍后播放”队列将会被清空\n\n其他\n- 升级React native到v0.68.2"}, {"version": "0.12.0", "desc": "新增\n- 为搜索、歌单、排行榜的歌曲菜单添加分享“分享歌曲”按钮\n- 新增设置-基本设置-分享设置，它用于控制歌曲菜单的分享行为，默认使用系统分享\n- 新增是否在通知栏显示歌曲图片设置，默认开启（原来的行为）\n- 新增黑色皮肤“黑灯瞎火”\n- 新增设置-基本设置-主题颜色-跟随系统亮、暗模式切换主题设置，注：此设置需要android 10或ios 13及以上的版本才支持\n\n优化\n- 现在即使切歌模式处于单曲循环、顺序播放、禁用时，手动切歌将会按照列表循环的规则处理（#69）\n- 添加定时退出计时结束后的提示\n\n修复\n- 修复wy源搜索某些歌曲时第一页之后的歌曲无法加载的问题\n- 每次启动时过滤无效的歌曲\n- 修复换源失败时的处理问题\n- 修复非循环模式下播放结束后的状态显示问题及无法重新播放的问题（#104）\n- 修复定时退出可能导致崩溃的问题\n- 修复播放详情页歌词界面在把应用切到后台再切回来会导致屏幕常亮失效的问题\n\n变更\n- 歌曲菜单的“复制歌曲名”改为“分享歌曲”，点击后可以选择第三方应用分享歌曲详情页链接\n- 已存在目录列表的歌曲再次添加时将不会变成移除\n\n其他\n- 升级react-native到 v0.68.1"}, {"version": "0.11.1", "desc": "修复\n- 修复播放栏在某些设备不显示的问题"}, {"version": "0.11.0", "desc": "新增\n- 新增“点击列表里的歌曲时自动切换到当前列表播放”设置，此功能仅对歌单、排行榜有效，默认关闭\n- 添加试听接口，这是测试接口、临时接口都不可用时最后的选择...\n\n优化\n- 过滤tx源某些不支持播放的歌曲，解决播放此类内容会导致意外的问题\n- 备份与恢复兼容单个列表文件的导入\n- 添加通知权限的检查提醒，点击“不再提示”后，将会在设置-清空缓存后才会恢复提示\n\n修复\n- 修复Android 12下的桌面歌词锁定后还是无法在应用外点击歌词后面下面的内容的问题\n\n其他\n- 升级React native到v0.67.4"}, {"version": "0.10.3", "desc": "优化\n- 优化kw源英文与翻译歌词的匹配\n\n修复\n- 修复桌面歌词播放器会导致应用崩溃的问题"}, {"version": "0.10.2", "desc": "修复\n- 修复某些系统下的虚拟导航栏会导致播放栏隐藏的问题（react-native v0.67.x导致的）\n\n其他\n- 降级react-native到 v0.66.4"}, {"version": "0.10.1", "desc": "优化\n- 优化通知栏的更新机制，尝试修复魅族的通知栏图片不显示的问题\n- 我的列表-列表名的右击菜单更新已收藏的在线列表时，将始终重新加载，不再使用缓存，解决在原平台更新歌单后，在LX点击更新可能看到的还是在原平台更新前的歌单的问题\n\n修复\n- 修复tx源无搜索结果的问题\n- 修复小米等设备下面的手势提示线背景颜色为黑色的问题\n\n其他\n- 升级React native到v0.67.1"}, {"version": "0.10.0", "desc": "新增\n- 同步功能新增对列表位置调整的支持（需v1.15.3以上的PC端版本才支持）\n- 新增播放详情页歌词字体大小调整设置，可在详情页右上角的按钮进行调整\n- 新增同步服务地址历史列表功能\n- 横屏播放详情页新增评论入口\n- 我的列表歌曲三个点的菜单新增复制歌曲名\n\n优化\n- 修改对播放模块的调用，杜绝应用显示正在播放的歌曲与实际播放歌曲不一致的问题（这是播放模块歌曲队列与应用内歌曲队列在某些情况下出现不一致时导致的）\n- 支持PC端同步功能添加对列表顺序调整的控制，确保手动调整位置后的列表与不同的电脑同步时，列表位置不会被还原\n- 调整横屏下的导航栏、播放详情页布局，提高屏幕空间利用率并使其更易操作\n- 调整歌单类别、我的列表弹出层界面\n- 播放栏移除上一曲按钮，将多出来的空间加给播放、下一曲按钮\n- 现在点击、长按播放栏歌曲标题也可以进入详情页、定位当前播放歌曲了\n\n修复\n- 修复kw源某些歌曲的歌词提取异常的问题\n\n其他\n- 升级react-native到v0.66.4"}, {"version": "0.9.2", "desc": "优化\n- 添加应用初始化出错时的错误捕获输出\n- 优化歌词自动换源机制\n\n修复\n- 修复因kw源歌词接口停用导致该源歌词获取失败的问题\n\n其他\n- 更新react-native到v0.66.3\n- 更新Exoplayer到v2.16.0"}, {"version": "0.9.1", "desc": "修复\n- 修复删除列表时会导致应用崩溃的问题\n- 修复原生代码导致的错误日志记录"}, {"version": "0.9.0", "desc": "新增\n- 新增歌曲评论显示，可在播放详情页进入。（与PC端一样，目前仅支持显示部分评论）\n- 新增播放、收藏整个排行榜功能，可长按排行榜名字后在弹出的菜单中操作\n- 新增单个列表导入/导出功能，可以方便分享歌曲列表，可在点击“我的列表”里的列表名右侧的按钮后弹出的菜单中使用\n- 新增删除列表前的确认弹窗，防止误删列表\n\n优化\n- 添加更多同步功能的日志记录\n\n修复\n- 修复kg源的歌单链接无法打开的问题\n- 修复同一首歌的URL、歌词等同时需要换源时的处理问题\n- 修复在排行榜页面无法时无法通过点击我的列表图标切换到我的列表的问题\n\n其他\n- 更新react-native到v0.66.1"}, {"version": "0.8.3", "desc": "修复\n- 修复我的列表搜索无法搜索小括号、中括号等字符，并会导致应用崩溃的问题\n- 修复使用同步功能同步完成后，列表没有被保存，导致下次再连接同步时被同步新增的歌曲被移除的问题（此问题由v0.8.2的存储切片改造引入的）\n\n其他\n- 更新React native到v0.66.0"}, {"version": "0.8.2", "desc": "优化\n- 缓冲进度条颜色\n- 优化数据存储，若需要存储的数据过大时会将数据切片后存储，现在存储大列表不会导致列表丢失了\n\n修复\n- 修复随机播放模式下在同列表切其他歌曲不会清空已播放列表的问题\n- 修复歌曲播放出错时的URL刷新问题"}, {"version": "0.8.1", "desc": "优化\n- 添加更多错误信息的记录\n\n修复\n- 修复潜在的获取缓存大小报错问题\n- 修复mg排行榜无法加载的问题\n- 修复列表导出失败时的提示信息缺失翻译的问题\n- 修复 Android 11 导入列表时，不显示备份文件的问题\n- 修复其他应用播放声音时，软件临时暂停播放后通知栏的状态仍显示正在播放的问题"}, {"version": "0.8.0", "desc": "新增\n- 添加对通知栏歌曲进度条的支持\n\n修复\n- 修复某些情况下桌面歌词会导致APP崩溃的问题\n- 修复从电脑浏览器复制的企鹅歌单链接无法打开的问题\n\n其他\n- 升级React native到v0.65.1\n- 升级播放模块`react-native-track-player`到v2版本，优化通知栏歌曲信息显示逻辑"}, {"version": "0.7.1", "desc": "修复\n- 修复无法从歌单界面打开网易歌单详情的问题"}, {"version": "0.7.0", "desc": "如果你喜欢并经常使用洛雪音乐，并想要第一时间尝鲜洛雪的新功能，可以加入测试企鹅群768786588，\n注意：测试版的功可能会不稳定，打算潜水的勿加。\n\n新增\n- 新增横屏状态下的播放详情页\n- 新增橙、粉、灰主题色\n- 新增桌面歌词的字体大小、透明度设置\n- 新增我的列表内歌曲搜索定位功能\n\n调整\n- 为了与搜索、歌单操作栏位置统一，现将我的列表-收藏的列表操作栏由底部挪到顶部\n\n修复\n- 修复tx源的歌词无法显示的问题\n- 修复随机播放模式下使用稍后播放功能会导致歌曲单曲循环的问题\n- 修复某些情况下桌面歌词会导致APP崩溃的问题"}, {"version": "0.6.2", "desc": "优化\n- 优化设置界面的输入框输入机制，现在只要键盘收起即可自动保存输入的内容\n- 添加在启用桌面歌词时对悬浮层权限的检查，这应该可以修复某些设备上点击启用桌面歌词时不显示无权限弹窗也不显示桌面歌词的情况\n\n变更\n- 不再自动聚焦定时退出、调整位置弹窗内的输入框，这应该可以修复某些设备无法在这两个地方弹出键盘的问题\n\n修复\n- 修复启用桌面歌词时的权限提示弹窗会导致应用报错的问题\n- 修复我的列表无法更新从收藏的排行榜的问题"}, {"version": "0.6.1", "desc": "修复\n- 修复随机播放下无法切歌的问题"}, {"version": "0.6.0", "desc": "新增\n- 新增局域网同步功能（实验性，首次使用前建议先备份一次列表），此功能需要配合PC端使用，移动端与PC端处在同一个局域网（路由器的网络）下时，可以多端实时同步歌曲列表，使用问题请看\"常见问题\"。\n- 新增桌面歌词\n\n优化\n- 优化退出应用的机制，现在在需要退出应用的场景将会完全退出应用\n\n修复\n- 修复某些情况下出现恢复播放信息失败的问题\n- 修复删除列表中正在播放的歌曲时会自动跳到第一首的问题\n- 修复因其他应用需要播放声音而暂停播放音乐时歌词不会暂停播放导致恢复播放后歌词与播放进度不一致的问题"}, {"version": "0.5.3", "desc": "修复\n- 修复歌曲缓存失效的问题"}, {"version": "0.5.2", "desc": "优化\n- 优化mg源打开歌单的链接兼容\n\n修复\n- 修复单曲循环播放时循环次数为偶数时歌词不重新播放的问题\n- 添加针对进入歌词界面时某些情况下会弹出`scrollToIndex out of range: requested index ...`崩溃错误弹窗的处理\n- 修复导入kg歌单最多只能加载100、500首歌曲的问题。注：现在可以加载1000+首歌曲的歌单，但出于未知原因会导致部分歌曲无法加载（可能是无版权导致的），目前酷狗码仍然最多只能加载500首歌"}, {"version": "0.5.1", "desc": "优化\n- 添加切换播放模式时的文字提示\n- 优化单首歌曲的添加弹窗操作，当选择当前歌曲已存在目标列表时（列表名灰色显示），会将当前歌曲从目标列表移除，否则将当前歌曲添加到目标列表，添加在弹窗内对歌曲的添加、移动、删除操作时的文字提示\n\n修复\n- 修复mg源搜索失效的问题\n\n移除\n- 因wy源的歌单列表已没有“最新”排序的选项，所以现跟随移除wy源歌单列表按“最新”排序的按钮"}, {"version": "0.5.0", "desc": "新增\n- 新增“其他应用播放声音时，自动暂停播放”设置，默认开启\n- 新增“添加歌曲到列表时的位置”设置，可选项为列表的“顶部”与“底部”\n- 新增“显示歌词翻译设置”，默认关闭\n\n变更\n- 添加歌曲到列表时从原来的底部改为顶部，若想要恢复原来的行为则可以去更改“添加歌曲到列表时的位置”设置项"}, {"version": "0.4.2", "desc": "优化\n- 优化wy源歌单导入匹配，现在存在链接外的其他字符也可以打开歌单了\n\n修复\n- 修复定时播放开启歌曲播放完毕再停止时，若倒计时已结束会导致无法播放歌曲的问题\n- 修复打开歌单失败时会导致应用崩溃的问题\n- 修复打开kw歌单失败时会无限重试的问题\n- 尝试修复弹出菜单、列表位置不正确的问题\n- 修复打开kg源歌单链接失败的问题\n- 尝试修复有时候进入播放详情歌词界面时会导致应用UI被冻结的问题\n- 修复有时候进入播放详情页时歌曲封面大小显示不正确的问题"}, {"version": "0.4.1", "desc": "修复\n- 修复定时播放开启歌曲播放完毕再停止时，若倒计时已结束会导致无法播放歌曲的问题"}, {"version": "0.4.0", "desc": "新增\n- 新增我的列表中已收藏的在线列表的更新功能。注意：这将会覆盖本地的目标列表，歌曲将被替换成最新的在线列表（与PC端的同步一样）\n- 歌曲添加、移动弹窗新增创建新列表功能\n- 新增定时退出播放\n\n优化\n- 优化应用布局对手机系统字体大小的适配\n- 调整歌单详情页，现在在歌单详情页按手机上的返回键将会返回歌单列表，而不是直接退出APP\n- 优化进入播放详情页、歌单详情页的动画效果\n\n修复\n- 尝试修复某些情况下进播放详情歌词界面时报错的问题"}, {"version": "0.3.3", "desc": "修复\n- 尝试修复软件启动时恢复上一次播放的歌曲可能导致软件崩溃的问题\n- 尝试修复播放详情页歌词导致UI冻结的问题\n- 修复企鹅音乐搜索歌曲没有结果的问题\n\n其他\n- 整合日志记录\n- 更新 exoPlayer 到 2.14.0"}, {"version": "0.3.2", "desc": "修复\n- 修复手机分享的wy歌单、某些tx、kg歌单无法打开的问题\n- 修复打开空的歌单时，点击播放全部会导致应用崩溃的问题\n- 修复企鹅音乐搜索歌曲没有结果的问题"}, {"version": "0.3.1", "desc": "修复\n- 修复进入播放详情歌词界面后的屏幕常亮不会被取消的问题"}, {"version": "0.3.0", "desc": "新增\n- 新增通过歌单链接打开歌单的功能\n\n优化\n- 切换到播放详情歌词界面时将阻止屏幕息屏\n\n修复\n- 修复一个导致崩溃日志写入文件前会导致APP崩溃的莫名其妙问题"}, {"version": "0.2.0", "desc": "新增\n- 新增竖屏下的播放详情页"}, {"version": "0.1.7", "desc": "优化\n- 修改歌单导入流程，添加对歌单导入错误的捕获\n\n修复\n- 修复在系统暗主题下，应用内文字输入框的字体会变成白色的问题"}, {"version": "0.1.6", "desc": "优化\n- 改进软件错误处理，添加对软件崩溃的错误日志记录，可在设置-其他查看错误日志历史。注：清理缓存时日志也将会被清理\n\n修复\n- 修复显示版本更新弹窗会导致应用崩溃的问题"}, {"version": "0.1.5", "desc": "修复\n- 修复修复协议弹窗可以被绕过的问题\n- 修复从在线列表使用稍后播放功能播放歌曲时，歌曲封面不显示的问题\n- 修复正在播放“稍后播放”的歌曲时，对“稍后播放”前播放的列表进行添加、删除操作会导致切歌的问题"}, {"version": "0.1.4", "desc": "修复\n- 修复获取在线列表时快速切换会导致APP闪退的问题"}, {"version": "0.1.3", "desc": "优化\n- 添加导入提示，兼容从PC端“全部数据”类型的备份文件中导入歌单\n- 添加全局异常错误捕获，现在一般情况下APP崩溃前会弹窗提示错误信息。"}, {"version": "0.1.2", "desc": "优化\n- 在搜索、歌单、排行榜列表多选音乐后点菜单中的播放将会把已选的歌曲添加到试听列表播放\n\n修复\n- 修复播放模块没拉取最新代码导致播放器存在无法从通知栏停止等问题"}, {"version": "0.1.1", "desc": "lx-music移动端v0.1.1版本发布 🎊 🎉"}]}