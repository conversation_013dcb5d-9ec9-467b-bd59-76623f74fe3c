{
  // Place your LxMusicMobile 工作区 snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  "Import translation": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "imtl",
    "body": ["import { useTranslation } from '@/plugins/i18n'", "$1const { t } = useTranslation()"],
    "description": "Translation Language",
  },
  "Import store setting": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "imss",
    "body": ["import settingState from '@/store/setting/state'"],
    "description": "Import store setting",
  },
  "Import store player": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "imsp",
    "body": ["import playerState from '@/store/player/state'"],
    "description": "Import store player",
  },
  "Import store list": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "imsl",
    "body": ["import listState from '@/store/list/state'"],
    "description": "Import store list",
  },
  "Import toast": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "imts",
    "body": ["import { toast } from '@/utils/tools'", "$1toast(t(''), 'long')"],
    "description": "Import toast",
  },
  "Use getter theme": {
    "scope": "javascript,typescript,typescriptreact",
    "prefix": "ugt",
    "body": ["const theme = useGetter('common', 'theme')"],
    "description": "Use getter theme",
  },
}
