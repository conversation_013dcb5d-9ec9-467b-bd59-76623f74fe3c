name: Setup
description: Setup Env

runs:
  using: composite
  steps:
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version-file: .nvmrc

    - name: Setup Java Env
      uses: actions/setup-java@v4
      with:
        distribution: 'microsoft'
        java-version: '17'
        cache: gradle

    - name: Get npm cache directory
      id: npm-cache-dir
      shell: bash
      run: echo "cachedir=$(npm config get cache)" >> ${GITHUB_OUTPUT}

    - name: Cache node modules
      id: cache-npm
      uses: actions/cache@v4
      with:
        # npm cache files are stored in `~/.npm` on Linux/macOS
        path: ${{ steps.npm-cache-dir.outputs.cachedir }}
        key: ${{ runner.os }}-npm-cache-${{ hashFiles('package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-npm-cache-

    - name: Install dependencies
      shell: bash
      run: npm ci
