import {
  rsaEncrypt,
  rsaDecrypt,
  RSA_PADDING,
  generateRsaKey,
  AES_MODE,
  aesEncrypt,
  aesDecrypt,
} from '@/utils/nativeModules/crypto'

const publicKey = `
-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA4U2m4fBhTkQOOeAlEusFCDa28UI3xZqv
5EGiOZCJ2bH1LfBjwG5dL3Zk2vT6XLaAn7vyXwVYNmdDn4Fa3l8fZndCty1aUAkpxZehZVy/0I+z
Q7QwSvzQpv2yHPQ76Kcuc3E7VEMSPZkx71dQpsDBtE/F04TW6zOxomFcbqUA97QsjNwU8KKSKKJR
2FhjEX0WhJpvDrkAKQBEujwf3pQDa8iUuF4F0v+oCKiSEf6tuWYx5iBpOvXUmZDLPeBnVZuvJM0e
2yXaIYeZorDaosIvCEqVcDPT3gvePZp6eTyffRJmqk7OkyG2epWM1XPXynu85BYK91pZ03YRNBrp
OkdU7wIDAQAB
-----END PUBLIC KEY-----
`
const privateKey = `
************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

// void rsaEncrypt(Buffer.from('hello').toString('base64'), publicKey, RSA_PADDING.OAEPWithSHA1AndMGF1Padding).then((text) => {
//   console.log(text)
//   void rsaDecrypt(text, privateKey, RSA_PADDING.OAEPWithSHA1AndMGF1Padding).then((text) => {
//     console.log(text)
//   })
// })
// void generateRsaKey().then((key) => {
//   console.log(key.publicKey)
//   console.log(key.privateKey)
// })

const aesKey = Buffer.from('123456789abcdefg').toString('base64')
const vi = Buffer.from('012345678901234a').toString('base64')

// void aesEncrypt(Buffer.from('hello').toString('base64'), aesKey, vi, AES_MODE.CBC_PKCS7Padding).then((text) => {
//   console.log('hello', text)
//   void aesDecrypt(text, aesKey, vi, AES_MODE.CBC_PKCS7Padding).then((text) => {
//     console.log(text)
//   })
// })

// void aesEncrypt(Buffer.from('hello2').toString('base64'), aesKey, '', AES_MODE.ECB_NoPadding).then((text) => {
//   console.log('hello2', text)
//   void aesDecrypt(text, aesKey, '', AES_MODE.ECB_NoPadding).then((text) => {
//     console.log(text)
//   })
// })
